import React, { useState, useEffect, useRef } from "react";
import Navbar from "../Navbar";
import Sidebar from "../Sidebar";
import DataTable from "react-data-table-component";
import axios from "axios";
import Papa from "papaparse";
import { useNavigate } from "react-router-dom";
import Swal from "sweetalert2";

export function ProcessG() {
  const apiUrl_4000 = import.meta.env.VITE_BACKEND_URL;

  const [isF2Pressed, setIsF2Pressed] = useState(false);
  const [isModified, setIsModified] = useState(false);
  const navigate = useNavigate();
  const [showCheckbox, setShowCheckbox] = useState(false);
  const [isButtonDisabled, setIsButtonDisabled] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [data, setData] = useState([]);
  const [editedData, setEditedData] = useState({});
  const [isChanged, setIsChanged] = useState(false);
  const editedDataRef = useRef(editedData);
  const [selectedRowForCopy, setSelectedRowForCopy] = useState(null); // For radio selection

  const fetchProcessg = async () => {
    try {
      const response = await axios.get(
        `${apiUrl_4000}/processg/fetch-processg`
      );
      // console.log("Fetched data:", response.data);
      setData(response.data.data.processg || []);
    } catch (error) {
      // console.error("Error fetching processg:", error);
    }
  };

  useEffect(() => {
    fetchProcessg();
  }, []);

  useEffect(() => {
    const initialEditedData = data.reduce((acc, row, index) => {
      if (!editedData[index]) {
        acc[index] = { ...row };
      }
      return acc;
    }, {});

    if (Object.keys(initialEditedData).length > 0) {
      setEditedData(initialEditedData);
    }
  }, [data]);

  const [formData, setFormData] = useState({
    ProcessG_CD: "",
    Change_CD: "",
    ManageG_CD: "",
    ProcessG_Name: "",
    ProcessG_Abb: "",
    ProcessG_Symbol: "",
    ProcessG_Mark: "",
    Use: false,
    Use_Object: false,
    Graph: false,
    List: false,
    Coefficient: "",
    M_Coefficient: "",
    P_Coefficient: "",
    Std_M_CAT: "",
    Std_M_Time: "",
    Std_P_CAT: "",
    Std_P_Time: "",
    M_Resource_N: "",
    S_Resource_N: "",
    ProcessG_Remark: "",
  });

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;

    // ตรวจสอบฟิลด์ที่ต้องห้ามเกิน 100
    if (
      name === "Std_M_CAT" ||
      name === "Std_P_CAT" ||
      name === "M_Resource_N" ||
      name === "S_Resource_N"
    ) {
      // เช็คว่าเป็นตัวเลขและไม่เกิน 100
      if (isNaN(value) || value < 0 || value > 100) {
        alert("กรุณากรอกค่าไม่เกิน 100");
        return; // หยุดการทำงานถ้าค่าผิด
      }
    }

    // อัพเดตข้อมูลตามปกติ
    setFormData({
      ...formData,
      [name]: type === "checkbox" ? checked : value, // กำหนดค่าของ checkbox หรือ input field
    });
  };

  const handleChange = (e, processGCD, fieldName) => {
    let newValue = e.target.value;

    // ตรวจสอบแค่ฟิลด์ที่ต้องการ (Std_M_CAT, Std_P_CAT, M_Resource_N, S_Resource_N)
    if (
      fieldName === "Std_M_CAT" ||
      fieldName === "Std_P_CAT" ||
      fieldName === "M_Resource_N" ||
      fieldName === "S_Resource_N"
    ) {
      // เช็คว่าเป็นตัวเลขและไม่เกิน 100
      if (isNaN(newValue) || newValue < 0 || newValue > 100) {
        alert("กรุณากรอกค่าไม่เกิน 100"); // แสดง alert ถ้าค่าผิด
        return; // หยุดการทำงานเพื่อไม่ให้อัพเดตค่า
      }
    }

    // อัพเดตข้อมูลใน state
    setEditedData((prevData) => ({
      ...prevData,
      [processGCD]: {
        ...prevData[processGCD],
        [fieldName]: newValue,
      },
    }));
  };

  const openModal = () => {
    if (selectedRowForCopy) {
      const { ProcessG_CD, ...rest } = selectedRowForCopy;
      setFormData({
        ProcessG_CD: "",
        ...rest,
      });
    } else {
      setFormData({
        ProcessG_CD: "",
        Change_CD: "",
        ManageG_CD: "",
        ProcessG_Name: "",
        ProcessG_Abb: "",
        ProcessG_Symbol: "",
        ProcessG_Mark: "",
        Use: false,
        Use_Object: false,
        Graph: false,
        List: false,
        Coefficient: "",
        M_Coefficient: "",
        P_Coefficient: "",
        Std_M_CAT: "",
        Std_M_Time: "",
        Std_P_CAT: "",
        Std_P_Time: "",
        M_Resource_N: "",
        S_Resource_N: "",
        ProcessG_Remark: "",
      });
    }
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  const handleSelectClick = () => {
    document.getElementById("F1").disabled = true;
    document.getElementById("F2").disabled = true;
    document.getElementById("F3").disabled = true;
    document.getElementById("F4").disabled = true;
    document.getElementById("F5").disabled = true;
    document.getElementById("F6").disabled = true;
    document.getElementById("F7").disabled = true;
    document.getElementById("F8").disabled = true;
    document.getElementById("F9").disabled = true;
    document.getElementById("F10").disabled = false;
    document.getElementById("F11").disabled = false;
    document.getElementById("F12").disabled = false;

    setIsButtonDisabled(false);
    handleCheckboxToggle();
  };

  const [selectedProcessGs, setSelectedProcessGs] = useState([]);

  const handleCheckboxChange = (e, processg) => {
    if (e.target.checked) {
      setSelectedProcessGs([...selectedProcessGs, processg]);
    } else {
      setSelectedProcessGs(selectedProcessGs.filter((cd) => cd !== processg));
    }
  };

  const handleCheckboxToggle = () => {
    setShowCheckbox(!showCheckbox);
  };

  const handleCheckboxChangeForEdit = (e, processgCd, field) => {
    const { checked } = e.target;

    // อัปเดตข้อมูลใน editedData หรือ state ของ row
    setEditedData((prevData) => ({
      ...prevData,
      [processgCd]: {
        ...prevData[processgCd],
        [field]: checked, // ใช้ค่า boolean ที่ได้จาก checkbox
      },
    }));
  };

  const handleSaveProcessGroup = async (e) => {
    e.preventDefault();

    // ตรวจสอบว่า ProcessG_Remark มีคำที่ไม่อนุญาตหรือไม่
    if (
      formData.ProcessG_Remark &&
      formData.ProcessG_Remark.includes("not allowed")
    ) {
      Swal.fire({
        title: "Error",
        text: "This remark contains an invalid word.",
        icon: "error",
        confirmButtonText: "OK",
      });
      return; // หยุดการทำงานหากพบคำที่ไม่อนุญาต
    }

    try {
      const token = localStorage.getItem("authToken");
      const response = await axios.post(
        `${apiUrl_4000}/processg/create-processg`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (response.status === 200) {
        Swal.fire({
          title: "Success",
          text: "Process Group created successfully.",
          icon: "success",
          confirmButtonText: "OK",
        });
        // รีเฟรชข้อมูลหลังบันทึกเสร็จ
        fetchProcessg();
        setFormData({
          ProcessG_CD: "",
          Change_CD: "",
          ManageG_CD: "",
          ProcessG_Name: "",
          ProcessG_Abb: "",
          ProcessG_Symbol: "",
          ProcessG_Mark: "",
          Use: false,
          Use_Object: false,
          Graph: false,
          List: false,
          Coefficient: "",
          M_Coefficient: "",
          P_Coefficient: "",
          Std_M_CAT: "",
          Std_M_Time: "",
          Std_P_CAT: "",
          Std_P_Time: "",
          M_Resource_N: "",
          S_Resource_N: "",
          ProcessG_Remark: "",
        });

        // Clear the selected radio button by setting selectedRowForCopy to null
        setSelectedRowForCopy(null);

        closeModal(); // ปิด modal หลังจากบันทึก
      }
    } catch (error) {
      console.error("Error creating Process Group:", error);
      Swal.fire({
        title: "Error",
        text:
          error.response?.data?.message || "Failed to create Process Group.",
        icon: "error",
        confirmButtonText: "OK",
      });
    }
  };

  const handleSave = async (processgCd) => {
    const payload = { ProcessG_CD: processgCd };
    let isEdited = false;

    const editedFields = Object.keys(editedData[processgCd] || {}); // คีย์ที่ถูกแก้ไข

    // ตรวจสอบว่ามีการแก้ไขข้อมูลหรือไม่
    for (const field of editedFields) {
      const newValue = editedData[processgCd]?.[field];
      const oldValue = data.find((row) => row.ProcessG_CD === processgCd)?.[
        field
      ];

      // ตรวจสอบว่าค่าเป็นเลขและเกิน 100
      if (
        (field === "Std_M_Time" ||
          field === "Std_P_Time" ||
          field === "M_Resource_N" ||
          field === "S_Resource_N" ||
          field === "Coefficient" ||
          field === "M_Coefficient" ||
          field === "P_Coefficient") &&
        (newValue > 100 || newValue < 0)
      ) {
        Swal.fire({
          icon: "error",
          title: "Invalid Value",
          text: `${field} cannot be greater than 100.`,
        });
        return; // หยุดการทำงานไม่ให้บันทึก
      }

      if (newValue !== oldValue) {
        // ตรวจสอบคำที่ไม่อนุญาต (ถ้ามี)
        if (
          newValue &&
          typeof newValue === "string" &&
          newValue.includes("not allowed")
        ) {
          Swal.fire({
            title: "Error",
            text: "This field contains an invalid word.",
            icon: "error",
            confirmButtonText: "OK",
          });
          return;
        }

        payload[field] = newValue === "" ? null : newValue; // อัปเดตค่าใหม่
        isEdited = true;
      }
    }

    // ถ้าไม่มีการแก้ไข
    if (!isEdited) {
      Swal.fire({
        icon: "info",
        title: "No changes made!",
        text: "Please make sure to edit something before saving.",
      });
      return;
    }

    let isSaving = false;

    if (isSaving) {
      return;
    }

    try {
      isSaving = true;

      // ส่งข้อมูลไปยัง API
      const token = localStorage.getItem("authToken");
      const response = await axios.put(
        `${apiUrl_4000}/processg/update-processg`,
        payload,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (response.status === 200) {
        // อัปเดตข้อมูลใน state
        const updatedData = [...data];
        const rowIndex = updatedData.findIndex(
          (row) => row.ProcessG_CD === processgCd
        );
        if (rowIndex !== -1) {
          Object.keys(payload).forEach((field) => {
            if (field !== "ProcessG_CD") {
              updatedData[rowIndex][field] = payload[field];
            }
          });
          setData(updatedData);
        }

        Swal.fire({
          icon: "success",
          title: "Edit Successfully!",
          text: "ProcessG data has been updated.",
        });
      }
    } catch (error) {
      Swal.fire({
        icon: "error",
        title: "Something went wrong!",
        text: "Please try again later.",
      });
      console.error(error);
    } finally {
      isSaving = false;
    }
  };

  const handleDeleteClick = async () => {
    if (selectedProcessGs.length === 0) {
      Swal.fire({
        title: "No Selection",
        text: "Please select at least one item to delete.",
        icon: "warning",
        confirmButtonText: "OK",
      });
      return;
    }

    const processgList = selectedProcessGs.join(", ");

    try {
      const confirmResult = await Swal.fire({
        title: "Confirm Delete",
        html: `Are you sure you want to delete the following items?<br>ProcessG_CD: <b>${processgList}</b>`,
        icon: "warning",
        showCancelButton: true,
        confirmButtonText: "Yes",
        cancelButtonText: "No",
      });

      if (confirmResult.isConfirmed) {
        const token = localStorage.getItem("authToken");

        const response = await axios.delete(
          `${apiUrl_4000}/processg/delete-processg`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
            data: selectedProcessGs.map((processG) => ({
              ProcessG_CD: processG,
            })), // ส่งข้อมูล ProcessG_CD
          }
        );

        if (response.status === 200) {
          Swal.fire({
            title: "Deleted!",
            text: `The selected ProcessG_CD have been deleted.`,
            icon: "success",
            confirmButtonText: "OK",
          });

          // อัปเดตข้อมูลใน state
          setData(
            data.filter((row) => !selectedProcessGs.includes(row.ProcessG_CD))
          );
          setSelectedProcessGs([]); // เคลียร์การเลือก
          document
            .querySelectorAll('input[type="checkbox"]')
            .forEach((checkbox) => {
              checkbox.checked = false; // รีเซ็ต checkbox ทั้งหมด
            });
        } else {
          Swal.fire({
            title: "Error",
            text: response.data.message || "An error occurred while deleting.",
            icon: "error",
            confirmButtonText: "OK",
          });
        }
      }
    } catch (error) {
      console.error("Error in handleDeleteClick:", error);
      Swal.fire({
        title: "Error",
        text: "Something went wrong while deleting.",
        icon: "error",
        confirmButtonText: "OK",
      });
    }
  };

  const handleF12Click = async () => {
    try {
      const confirmResult = await Swal.fire({
        title: "Confirm",
        html: isModified
          ? "The data has been edited. Do you want to go back to master 3?<br>ข้อมูลถูกแก้ไขต้องการกลับไปที่หน้า master 3 หรือไม่?<br>データは編集されました。master 3 に戻りますか？"
          : "Do you want to go back to master 3?<br>คุณต้องการกลับไปที่หน้า master 3 หรือไม่?<br>master 3 に戻りますか？",
        icon: "warning",
        showCancelButton: true,
        confirmButtonText: "Yes",
        cancelButtonText: "No",
      });

      if (confirmResult.isConfirmed) {
        setIsModified(false);
        navigate("/master3");
      }
    } catch (error) {
      console.error("Error in handleF12Click:", error);
      Swal.fire({
        title: "Error",
        text: "Try again.",
        icon: "error",
        confirmButtonText: "OK",
      });
    }
  };

  // ฟังก์ชันสำหรับ Export ข้อมูลเป็น CSV
  const exportToCsv = () => {
    const csvData = data.map((row) => ({
      ProcessG_CD: row.ProcessG_CD,
      Change_CD: row.Change_CD,
      ManageG_CD: row.ManageG_CD,
      ProcessG_Name: row.ProcessG_Name,
      ProcessG_Abb: row.ProcessG_Abb,
      ProcessG_Symbol: row.ProcessG_Symbol,
      ProcessG_Mark: row.ProcessG_Mark,
      Use: row.Use,
      Use_Object: row.Use_Object,
      Graph: row.Graph,
      List: row.List,
      Coefficient: row.Coefficient,
      M_Coefficient: row.M_Coefficient,
      P_Coefficient: row.P_Coefficient,
      Std_M_CAT: row.Std_M_CAT,
      Std_M_Time: row.Std_M_Time,
      Std_P_CAT: row.Std_P_CAT,
      Std_P_Time: row.Std_P_Time,
      M_Resource_N: row.M_Resource_N,
      S_Resource_N: row.S_Resource_N,
      ProcessG_Remark: row.ProcessG_Remark,
    }));

    const csv = Papa.unparse(csvData); // แปลง JSON เป็น CSV
    const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" });

    // ดาวน์โหลดไฟล์ CSV
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", "ProcessG_data.csv");
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const filteredData = data.filter((row) => {
    return Object.values(row).some((value) =>
      String(value).toLowerCase().includes(searchTerm.toLowerCase())
    );
  });

  const columns = [
    {
      name: "SelectForCopy",
      selector: (row) => (
        <input
          type="radio"
          name="row-radio"
          checked={
            selectedRowForCopy &&
            selectedRowForCopy.ProcessG_CD === row.ProcessG_CD
          }
          onChange={() => setSelectedRowForCopy(row)}
        />
      ),
      width: "150px",
      omit: false,
    },
    {
      name: "SelectForDelete",
      selector: (row) =>
        showCheckbox ? (
          <input
            type="checkbox"
            className="checkbox"
            onChange={(e) => handleCheckboxChange(e, row.ProcessG_CD)}
          />
        ) : null,
      width: "150px",
      omit: !showCheckbox,
    },
    {
      name: "ProcessG_CD",
      selector: (row) => (
        <input
          className="w-full p-2 bg-transparent"
          type="text"
          value={
            editedData[row.ProcessG_CD]?.ProcessG_CD !== undefined
              ? editedData[row.ProcessG_CD]?.ProcessG_CD
              : row.ProcessG_CD || ""
          }
          onChange={(e) => handleChange(e, row.ProcessG_CD, "ProcessG_CD")}
          disabled
        />
      ),
      width: "170px",
      sortable: true,
      sortFunction: (rowA, rowB) => {
        const valA =
          editedData[rowA.ProcessG_CD]?.ProcessG_CD || rowA.ProcessG_CD || "";
        const valB =
          editedData[rowB.ProcessG_CD]?.ProcessG_CD || rowB.ProcessG_CD || "";

        return valA.localeCompare(valB);
      },
    },
    {
      name: "Change_CD",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.ProcessG_CD]?.Change_CD !== undefined
              ? editedData[row.ProcessG_CD]?.Change_CD
              : row.Change_CD || ""
          }
          onChange={(e) => handleChange(e, row.ProcessG_CD, "Change_CD")}
          disabled={!isF2Pressed}
        />
      ),
      width: "190px",
    },
    {
      name: "ManageG_CD",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.ProcessG_CD]?.ManageG_CD !== undefined
              ? editedData[row.ProcessG_CD]?.ManageG_CD
              : row.ManageG_CD || ""
          }
          onChange={(e) => handleChange(e, row.ProcessG_CD, "ManageG_CD")}
          disabled={!isF2Pressed}
        />
      ),
      width: "190px",
    },
    {
      name: "ProcessG_Name",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.ProcessG_CD]?.ProcessG_Name !== undefined
              ? editedData[row.ProcessG_CD]?.ProcessG_Name
              : row.ProcessG_Name || ""
          }
          onChange={(e) => handleChange(e, row.ProcessG_CD, "ProcessG_Name")}
          disabled={!isF2Pressed}
        />
      ),
      width: "190px",
    },
    {
      name: "ProcessG_Abb",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.ProcessG_CD]?.ProcessG_Abb !== undefined
              ? editedData[row.ProcessG_CD]?.ProcessG_Abb
              : row.ProcessG_Abb || ""
          }
          onChange={(e) => handleChange(e, row.ProcessG_CD, "ProcessG_Abb")}
          disabled={!isF2Pressed}
        />
      ),
      width: "190px",
    },
    {
      name: "ProcessG_Symbol",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.ProcessG_CD]?.ProcessG_Symbol !== undefined
              ? editedData[row.ProcessG_CD]?.ProcessG_Symbol
              : row.ProcessG_Symbol || ""
          }
          onChange={(e) => handleChange(e, row.ProcessG_CD, "ProcessG_Symbol")}
          disabled={!isF2Pressed}
        />
      ),
      width: "190px",
    },
    {
      name: "ProcessG_Mark",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.ProcessG_CD]?.ProcessG_Mark !== undefined
              ? editedData[row.ProcessG_CD]?.ProcessG_Mark
              : row.ProcessG_Mark || ""
          }
          onChange={(e) => handleChange(e, row.ProcessG_CD, "ProcessG_Mark")}
          disabled={!isF2Pressed}
        />
      ),
      width: "190px",
    },
    {
      name: "Use",
      selector: (row) => (
        <input
          type="checkbox"
          checked={editedData[row.ProcessG_CD]?.Use ?? row.Use}
          className="mx-auto"
          onChange={(e) =>
            handleCheckboxChangeForEdit(e, row.ProcessG_CD, "Use")
          }
          disabled={!isF2Pressed}
        />
      ),
      width: "120px",
    },
    {
      name: "Use_Object",
      selector: (row) => (
        <input
          type="checkbox"
          checked={editedData[row.ProcessG_CD]?.Use_Object ?? row.Use_Object}
          className="mx-auto"
          onChange={(e) =>
            handleCheckboxChangeForEdit(e, row.ProcessG_CD, "Use_Object")
          }
          disabled={!isF2Pressed}
        />
      ),
      width: "150px",
    },
    {
      name: "Graph",
      selector: (row) => (
        <input
          type="checkbox"
          checked={editedData[row.ProcessG_CD]?.Graph ?? row.Graph}
          className="mx-auto"
          onChange={(e) =>
            handleCheckboxChangeForEdit(e, row.ProcessG_CD, "Graph")
          }
          disabled={!isF2Pressed}
        />
      ),
      width: "100px",
    },
    {
      name: "List",
      selector: (row) => (
        <input
          type="checkbox"
          checked={editedData[row.ProcessG_CD]?.List ?? row.List}
          className="mx-auto"
          onChange={(e) =>
            handleCheckboxChangeForEdit(e, row.ProcessG_CD, "List")
          }
          disabled={!isF2Pressed}
        />
      ),
      width: "150px",
    },
    {
      name: "Coefficient",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.ProcessG_CD]?.Coefficient !== undefined
              ? editedData[row.ProcessG_CD]?.Coefficient
              : row.Coefficient || ""
          }
          onChange={(e) => handleChange(e, row.ProcessG_CD, "Coefficient")}
          disabled={!isF2Pressed}
        />
      ),
      width: "190px",
    },
    {
      name: "M_Coefficient",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.ProcessG_CD]?.M_Coefficient !== undefined
              ? editedData[row.ProcessG_CD]?.M_Coefficient
              : row.M_Coefficient || ""
          }
          onChange={(e) => handleChange(e, row.ProcessG_CD, "M_Coefficient")}
          disabled={!isF2Pressed}
        />
      ),
      width: "190px",
    },
    {
      name: "P_Coefficient",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.ProcessG_CD]?.P_Coefficient !== undefined
              ? editedData[row.ProcessG_CD]?.P_Coefficient
              : row.P_Coefficient || ""
          }
          onChange={(e) => handleChange(e, row.ProcessG_CD, "P_Coefficient")}
          disabled={!isF2Pressed}
        />
      ),
      width: "190px",
    },
    {
      name: "Std_M_CAT",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="number"
          value={
            editedData[row.ProcessG_CD]?.Std_M_CAT !== undefined
              ? editedData[row.ProcessG_CD]?.Std_M_CAT
              : row.Std_M_CAT || ""
          }
          onChange={(e) => handleChange(e, row.ProcessG_CD, "Std_M_CAT")}
          disabled={!isF2Pressed}
        />
      ),
      width: "190px",
    },
    {
      name: "Std_M_Time",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="number"
          value={
            editedData[row.ProcessG_CD]?.Std_M_Time !== undefined
              ? editedData[row.ProcessG_CD]?.Std_M_Time
              : row.Std_M_Time ?? ""
          }
          onChange={(e) => handleChange(e, row.ProcessG_CD, "Std_M_Time")}
          disabled={!isF2Pressed}
        />
      ),
      width: "190px",
    },
    {
      name: "Std_P_CAT",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="number"
          value={
            editedData[row.ProcessG_CD]?.Std_P_CAT !== undefined
              ? editedData[row.ProcessG_CD]?.Std_P_CAT
              : row.Std_P_CAT ?? ""
          }
          onChange={(e) => handleChange(e, row.ProcessG_CD, "Std_P_CAT")}
          disabled={!isF2Pressed}
        />
      ),
      width: "190px",
    },
    {
      name: "Std_P_Time",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="number"
          value={
            editedData[row.ProcessG_CD]?.Std_P_Time !== undefined
              ? editedData[row.ProcessG_CD]?.Std_P_Time
              : row.Std_P_Time ?? ""
          }
          onChange={(e) => handleChange(e, row.ProcessG_CD, "Std_P_Time")}
          disabled={!isF2Pressed}
        />
      ),
      width: "190px",
    },
    {
      name: "M_Resource_N",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="number"
          value={
            editedData[row.ProcessG_CD]?.M_Resource_N !== undefined
              ? editedData[row.ProcessG_CD]?.M_Resource_N
              : row.M_Resource_N ?? ""
          }
          onChange={(e) => handleChange(e, row.ProcessG_CD, "M_Resource_N")}
          disabled={!isF2Pressed}
        />
      ),
      width: "190px",
    },
    {
      name: "S_Resource_N",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="number"
          value={
            editedData[row.ProcessG_CD]?.S_Resource_N !== undefined
              ? editedData[row.ProcessG_CD]?.S_Resource_N
              : row.S_Resource_N ?? ""
          }
          onChange={(e) => handleChange(e, row.ProcessG_CD, "S_Resource_N")}
          disabled={!isF2Pressed}
        />
      ),
      width: "190px",
    },
    {
      name: "ProcessG_Remark",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          style={{
            width: "fit-content",
            minWidth: "340px",
            maxWidth: "100%",
          }}
          value={
            editedData[row.ProcessG_CD]?.ProcessG_Remark !== undefined
              ? editedData[row.ProcessG_CD]?.ProcessG_Remark
              : row.ProcessG_Remark || ""
          }
          onChange={(e) => handleChange(e, row.ProcessG_CD, "ProcessG_Remark")}
          disabled={!isF2Pressed}
        />
      ),
      width: "400px",
    },
  ];

  return (
    <div className="flex bg-[#E9EFEC] h-[100vh]">
      <Sidebar />
      <div className="flex flex-col w-full mr-2 ml-2">
        <Navbar />
        <div className="flex-1 flex-col p-2 overflow-x-auto">
          <div className="bg-white grid grid-cols-1">
            <div className="bg-white grid grid-cols-1">
              <h1 className="text-2xl font-bold text-center mt-3">
                ProcessG <br /> 工程Gマスタ
              </h1>
              <hr className="my-6 h-0.5 bg-gray-500 opacity-100 dark:opacity-50 border-y-[1px] border-gray-300" />

              <div className="ml-5 text-lg flex justify-between">
                <input
                  className="border-2 border-gray-500 rounded-md w-52 h-9"
                  type="text"
                  placeholder=" Search..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
                <button
                  onClick={exportToCsv}
                  className="bg-blue-500 text-white px-4 py-2 rounded-md mr-5"
                >
                  Export to CSV
                </button>
              </div>

              <div className="flex justify-center items-center mt-5">
                <div className="w-full text-center px-5">
                  <DataTable
                    columns={columns}
                    data={filteredData}
                    pagination
                    paginationPerPage={10}
                    paginationRowsPerPageOptions={[10, 15, 20, 25, 30]}
                    customStyles={{
                      rows: {
                        style: {
                          "&:nth-of-type(odd)": { backgroundColor: "#ffffff" },
                          "&:nth-of-type(even)": { backgroundColor: "#F5F5F5" },
                          minHeight: "50px",
                          textAlign: "center",
                          justifyContent: "center",
                          borderBottom: "1px solid #ccc",
                          borderRight: "1px solid #ccc",
                        },
                      },
                      headCells: {
                        style: {
                          backgroundColor: "#DCDCDC",
                          fontSize: "14px",
                          textAlign: "center",
                          justifyContent: "center",
                          border: "1px solid #ccc",
                        },
                      },
                      cells: {
                        style: {
                          textAlign: "center",
                          justifyContent: "center",
                          border: "1px solid #ccc",
                        },
                      },
                      table: {
                        style: {
                          borderCollapse: "collapse",
                        },
                      },
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="bg-white p-3 mt-5">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-6 lg:grid-cols-12 gap-4">
            {/* First button group */}
            <button
              id="F1"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              disabled
            >
              (F1)
            </button>
            <button
              id="F2"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={() => {
                setIsF2Pressed(true); // เปลี่ยนสถานะเมื่อกด F2
              }}
              disabled={isF2Pressed}
            >
              Edit <br />
              編集 (F2)
            </button>
            <button
              id="F3"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={openModal}
              disabled={isF2Pressed}
            >
              New Add <br />
              新規追加 (F3)
            </button>

            {/* Modal */}
            {isModalOpen && (
              <div className="fixed inset-0 flex justify-center items-center bg-gray-500 bg-opacity-50">
                <div
                  className="bg-white p-6 rounded-lg w-1/2 max-h-[80vh] overflow-y-auto"
                  onClick={(e) => e.stopPropagation()}
                >
                  <h2 className="text-xl mb-4">Add New Process Group</h2>
                  <form onSubmit={handleSaveProcessGroup}>
                    {/* ProcessG_CD */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        ProcessG_CD
                      </label>
                      <input
                        type="text"
                        name="ProcessG_CD"
                        className="w-full p-2 border rounded-md"
                        value={formData.ProcessG_CD}
                        onChange={handleInputChange}
                      />
                    </div>

                    {/* Change_CD */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Change_CD
                      </label>
                      <input
                        type="text"
                        name="Change_CD"
                        className="w-full p-2 border rounded-md"
                        value={formData.Change_CD}
                        onChange={handleInputChange}
                      />
                    </div>

                    {/* ManageG_CD */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        ManageG_CD
                      </label>
                      <input
                        type="text"
                        name="ManageG_CD"
                        className="w-full p-2 border rounded-md"
                        value={formData.ManageG_CD}
                        onChange={handleInputChange}
                      />
                    </div>

                    {/* ProcessG_Name */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        ProcessG_Name
                      </label>
                      <input
                        type="text"
                        name="ProcessG_Name"
                        className="w-full p-2 border rounded-md"
                        value={formData.ProcessG_Name}
                        onChange={handleInputChange}
                      />
                    </div>

                    {/* ProcessG_Abb */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        ProcessG_Abb
                      </label>
                      <input
                        type="text"
                        name="ProcessG_Abb"
                        className="w-full p-2 border rounded-md"
                        value={formData.ProcessG_Abb}
                        onChange={handleInputChange}
                      />
                    </div>

                    {/* ProcessG_Symbol */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        ProcessG_Symbol
                      </label>
                      <input
                        type="text"
                        name="ProcessG_Symbol"
                        className="w-full p-2 border rounded-md"
                        value={formData.ProcessG_Symbol}
                        onChange={handleInputChange}
                      />
                    </div>

                    {/* ProcessG_Mark */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        ProcessG_Mark
                      </label>
                      <input
                        type="text"
                        name="ProcessG_Mark"
                        className="w-full p-2 border rounded-md"
                        value={formData.ProcessG_Mark}
                        onChange={handleInputChange}
                      />
                    </div>

                    {/* Use */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Use
                      </label>
                      <input
                        type="checkbox"
                        name="Use"
                        className="p-2 border rounded-md"
                        checked={formData.Use}
                        onChange={handleInputChange}
                      />
                    </div>

                    {/* Use_Object */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Use_Object
                      </label>
                      <input
                        type="checkbox"
                        name="Use_Object"
                        className="p-2 border rounded-md"
                        checked={formData.Use_Object}
                        onChange={handleInputChange}
                      />
                    </div>

                    {/* Graph */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Graph
                      </label>
                      <input
                        type="checkbox"
                        name="Graph"
                        className="p-2 border rounded-md"
                        checked={formData.Graph}
                        onChange={handleInputChange}
                      />
                    </div>

                    {/* List */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        List
                      </label>
                      <input
                        type="checkbox"
                        name="List"
                        className="p-2 border rounded-md"
                        checked={formData.List}
                        onChange={handleInputChange}
                      />
                    </div>

                    {/* Coefficient */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Coefficient
                      </label>
                      <input
                        type="number"
                        name="Coefficient"
                        className="w-full p-2 border rounded-md"
                        value={formData.Coefficient}
                        onChange={handleInputChange}
                      />
                    </div>

                    {/* M_Coefficient */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        M_Coefficient
                      </label>
                      <input
                        type="number"
                        name="M_Coefficient"
                        className="w-full p-2 border rounded-md"
                        value={formData.M_Coefficient}
                        onChange={handleInputChange}
                      />
                    </div>

                    {/* P_Coefficient */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        P_Coefficient
                      </label>
                      <input
                        type="number"
                        name="P_Coefficient"
                        className="w-full p-2 border rounded-md"
                        value={formData.P_Coefficient}
                        onChange={handleInputChange}
                      />
                    </div>

                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Std_M_CAT
                      </label>
                      <input
                        type="text"
                        name="Std_M_CAT"
                        className="w-full p-2 border rounded-md"
                        value={formData.Std_M_CAT}
                        onChange={handleInputChange}
                      />
                    </div>

                    {/* Std_M_Time */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Std_M_Time
                      </label>
                      <input
                        type="number"
                        name="Std_M_Time"
                        className="w-full p-2 border rounded-md"
                        value={formData.Std_M_Time}
                        onChange={handleInputChange}
                      />
                    </div>

                    {/* Std_P_CAT */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Std_P_CAT
                      </label>
                      <input
                        type="text"
                        name="Std_P_CAT"
                        className="w-full p-2 border rounded-md"
                        value={formData.Std_P_CAT}
                        onChange={handleInputChange}
                      />
                    </div>

                    {/* Std_P_Time */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Std_P_Time
                      </label>
                      <input
                        type="number"
                        name="Std_P_Time"
                        className="w-full p-2 border rounded-md"
                        value={formData.Std_P_Time}
                        onChange={handleInputChange}
                      />
                    </div>

                    {/* M_Resource_N */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        M_Resource_N
                      </label>
                      <input
                        type="text"
                        name="M_Resource_N"
                        className="w-full p-2 border rounded-md"
                        value={formData.M_Resource_N}
                        onChange={handleInputChange}
                      />
                    </div>

                    {/* S_Resource_N */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        S_Resource_N
                      </label>
                      <input
                        type="text"
                        name="S_Resource_N"
                        className="w-full p-2 border rounded-md"
                        value={formData.S_Resource_N}
                        onChange={handleInputChange}
                      />
                    </div>

                    {/* ProcessG_Remark */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        ProcessG_Remark
                      </label>
                      <textarea
                        name="ProcessG_Remark"
                        className="w-full p-2 border rounded-md h-24 overflow-y-auto"
                        value={formData.ProcessG_Remark}
                        onChange={handleInputChange}
                      />
                    </div>

                    {/* Save and Close Buttons */}
                    <div className="mt-4 flex justify-end gap-4">
                      <button
                        type="submit"
                        className="bg-blue-500 p-3 rounded-lg text-white"
                      >
                        Save
                      </button>
                      <button
                        type="button"
                        onClick={closeModal}
                        className="bg-gray-500 p-3 rounded-lg text-white"
                      >
                        Close
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            )}

            <button
              id="F4"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={handleSelectClick}
              disabled={isF2Pressed}
            >
              Select <br />
              選択 (F4)
            </button>

            <button
              id="F5"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              disabled
            >
              (F5)
            </button>
            <button
              id="F6"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              disabled
            >
              (F6)
            </button>
            <button
              id="F7"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              disabled
            >
              (F7)
            </button>
            <button
              id="F8"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              disabled
            >
              (F8)
            </button>
            <button
              id="F9"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={() => {
                data.forEach((row) => {
                  Object.keys(row).forEach((field) => {
                    if (editedData[row.ProcessG_CD]?.[field] !== undefined) {
                      handleSave(row.ProcessG_CD, field);
                    }
                  });
                });
              }}
              disabled={!isF2Pressed} // จะเปิดใช้งาน F9 เมื่อกด F2 แล้ว
            >
              Save <br />
              保存 (F9)
            </button>
            <button
              id="F10"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={handleDeleteClick}
              disabled={isButtonDisabled} // ตั้งค่าปุ่มให้ disabled ตามสถานะ
            >
              Delete <br />
              消去 (F10)
            </button>
            <button
              id="F11"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={() => {
                window.location.reload(); // รีเฟรชหน้า
              }}
              disabled={false} // ตั้งค่า disabled เป็น false ถ้าคุณต้องการให้ปุ่มทำงาน
            >
              Clear <br />
              クリア (F11)
            </button>

            <button
              id="F12"
              onClick={handleF12Click}
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
            >
              Back <br />
              戻る (F12)
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
