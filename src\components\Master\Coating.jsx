import React, { useState, useEffect, useRef } from "react";
import Navbar from "../Navbar";
import Sidebar from "../Sidebar";
import DataTable from "react-data-table-component";
import axios from "axios";
import Papa from "papaparse";
import { useNavigate } from "react-router-dom";
import Swal from "sweetalert2";

export function Coating() {
  const apiUrl_4000 = import.meta.env.VITE_BACKEND_URL;

  const [isF2Pressed, setIsF2Pressed] = useState(false);
  const [isModified, setIsModified] = useState(false);
  const navigate = useNavigate();
  const [showCheckbox, setShowCheckbox] = useState(false);
  const [isButtonDisabled, setIsButtonDisabled] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [data, setData] = useState([]);
  const [editedData, setEditedData] = useState({});
  const [isChanged, setIsChanged] = useState(false); // สถานะการเปลี่ยนแปลงของข้อมูล
  const [selectedRowForCopy, setSelectedRowForCopy] = useState(null); // For radio selection

  const fetchCoating = async () => {
    try {
      const response = await axios.get(`${apiUrl_4000}/coating/fetch-coating`);
      // console.log("Fetched data:", response.data);
      setData(response.data.data.coating || []);
    } catch (error) {
      // console.error("Error fetching coating:", error);
    }
  };

  useEffect(() => {
    fetchCoating();
  }, []);

  useEffect(() => {
    if (Object.keys(editedData).length === 0 && data.length > 0) {
      const initialEditedData = data.reduce((acc, row) => {
        acc[row.Coating_CD] = { ...row };
        return acc;
      }, {});
      setEditedData(initialEditedData);
    }
  }, [data]);

  const [formData, setFormData] = useState({
    Coating_CD: "",
    Coating_Name: "",
    Coating_Abb: "",
    Coating_Symbol: "",
    Coating_Remark: "",
  });

  const handleChange = (e, coatingCd, field) => {
    const { value } = e.target;

    // กำหนดความยาวสูงสุดสำหรับแต่ละฟิลด์
    const maxLengths = {
      Coating_CD: 2,
      Coating_Name: 10,
      Coating_Abb: 5,
      Coating_Symbol: 3,
      Coating_Remark: 20,
    };

    // ตรวจสอบความยาวของค่าที่ใส่
    if (value.length <= maxLengths[field]) {
      setEditedData({
        ...editedData,
        [coatingCd]: {
          ...editedData[coatingCd],
          [field]: value,
        },
      });
    } else {
      Swal.fire({
        title: "Input Too Long",
        text: `The ${field} field cannot exceed ${maxLengths[field]} characters.`,
        icon: "warning",
        confirmButtonText: "OK",
      });
    }
  };

  const openModal = () => {
    if (selectedRowForCopy) {
      const { Coating_CD, ...rest } = selectedRowForCopy;
      setFormData({
        Coating_CD: "",
        ...rest,
      });
    } else {
      setFormData({
        Coating_CD: "",
        Coating_Name: "",
        Coating_Abb: "",
        Coating_Symbol: "",
        Coating_Remark: "",
      });
    }
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  const handleSelectClick = () => {
    document.getElementById("F1").disabled = true;
    document.getElementById("F2").disabled = true;
    document.getElementById("F3").disabled = true;
    document.getElementById("F4").disabled = true;
    document.getElementById("F5").disabled = true;
    document.getElementById("F6").disabled = true;
    document.getElementById("F7").disabled = true;
    document.getElementById("F8").disabled = true;
    document.getElementById("F9").disabled = true;
    document.getElementById("F10").disabled = false;
    document.getElementById("F11").disabled = false;
    document.getElementById("F12").disabled = false;

    setIsButtonDisabled(false);
    handleCheckboxToggle();
  };

  const [selectedCoatings, setSelectedCoatings] = useState([]);

  const handleCheckboxChange = (e, coating) => {
    if (e.target.checked) {
      setSelectedCoatings([...selectedCoatings, coating]);
    } else {
      setSelectedCoatings(selectedCoatings.filter((cd) => cd !== coating));
    }
  };

  const handleSaveCoating = async (e) => {
    e.preventDefault();

    // ตรวจสอบความยาวและค่าที่ไม่ถูกต้อง
    if (!formData.Coating_CD || formData.Coating_CD.length > 2) {
      Swal.fire({
        title: "Error",
        text: "Coating_CD is required and must not exceed 2 characters.",
        icon: "error",
        confirmButtonText: "OK",
      });
      return;
    }

    if (formData.Coating_Name && formData.Coating_Name.length > 10) {
      Swal.fire({
        title: "Error",
        text: "Coating_Name must not exceed 10 characters.",
        icon: "error",
        confirmButtonText: "OK",
      });
      return;
    }

    if (formData.Coating_Abb && formData.Coating_Abb.length > 5) {
      Swal.fire({
        title: "Error",
        text: "Coating_Abb must not exceed 5 characters.",
        icon: "error",
        confirmButtonText: "OK",
      });
      return;
    }

    if (formData.Coating_Symbol && formData.Coating_Symbol.length > 3) {
      Swal.fire({
        title: "Error",
        text: "Coating_Symbol must not exceed 3 characters.",
        icon: "error",
        confirmButtonText: "OK",
      });
      return;
    }

    if (formData.Coating_Remark && formData.Coating_Remark.length > 20) {
      Swal.fire({
        title: "Error",
        text: "Coating_Remark must not exceed 20 characters.",
        icon: "error",
        confirmButtonText: "OK",
      });
      return;
    }

    try {
      const token = localStorage.getItem("authToken");
      const response = await axios.post(
        `${apiUrl_4000}/coating/create-coating`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (response.status === 200) {
        Swal.fire({
          title: "Success",
          text: "Coating created successfully.",
          icon: "success",
          confirmButtonText: "OK",
        });

        fetchCoating(); // โหลดข้อมูลใหม่
        setFormData({
          Coating_CD: "",
          Coating_Name: "",
          Coating_Abb: "",
          Coating_Symbol: "",
          Coating_Remark: "",
        });

        // Clear the selected radio button by setting selectedRowForCopy to null
        setSelectedRowForCopy(null);

        closeModal(); // ปิด Modal
      }
    } catch (error) {
      console.error("Error creating Coating:", error);

      Swal.fire({
        title: "Error",
        text: error.response?.data?.message || "Failed to create Coating.",
        icon: "error",
        confirmButtonText: "OK",
      });
    }
  };

  const handleCheckboxToggle = () => {
    setShowCheckbox(!showCheckbox);
  };

  const handleSave = async (coatingCd, field) => {
    // ตรวจสอบว่าค่าที่ถูกแก้ไขนั้นเปลี่ยนแปลงจากเดิมหรือไม่
    const newValue = editedData[coatingCd]?.[field];
    const oldValue = data.find((row) => row.Coating_CD === coatingCd)?.[field];

    if (newValue !== oldValue) {
      // ตรวจสอบคำที่ไม่อนุญาต (ถ้ามี)
      if (newValue && newValue.includes("not allowed")) {
        Swal.fire({
          title: "Error",
          text: "This field contains an invalid word.",
          icon: "error",
          confirmButtonText: "OK",
        });
        return; // หยุดการทำงานหากพบคำที่ไม่อนุญาต
      }

      try {
        const token = localStorage.getItem("authToken");
        const payload = {
          Coating_CD: coatingCd,
          [field]: newValue === "" ? null : newValue,
        };

        const response = await axios.put(
          `${apiUrl_4000}/coating/update-coating`,
          payload,
          {
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
          }
        );

        if (response.status === 200) {
          Swal.fire({
            title: "Success",
            text: "Coating updated successfully.",
            icon: "success",
            confirmButtonText: "OK",
          });

          // อัปเดตข้อมูลใน state
          const updatedData = [...data];
          const rowIndex = updatedData.findIndex(
            (row) => row.Coating_CD === coatingCd
          );
          if (rowIndex !== -1) {
            updatedData[rowIndex][field] = newValue;
            setData(updatedData);
          }

          setIsChanged(false);
        }
      } catch (error) {
        console.error("Error updating coating:", error);
        Swal.fire({
          title: "Error",
          text: error.response?.data?.message || "Failed to update coating.",
          icon: "error",
          confirmButtonText: "OK",
        });
      }
    }
  };

  const handleDeleteClick = async () => {
    if (selectedCoatings.length === 0) {
      Swal.fire({
        title: "No Selection",
        text: "Please select at least one item to delete.",
        icon: "warning",
        confirmButtonText: "OK",
      });
      return;
    }

    const coatingList = selectedCoatings.join(", ");

    try {
      const confirmResult = await Swal.fire({
        title: "Confirm Delete",
        html: `Are you sure you want to delete the following items?<br>Coating_CD: <b>${coatingList}</b>`,
        icon: "warning",
        showCancelButton: true,
        confirmButtonText: "Yes",
        cancelButtonText: "No",
      });

      if (confirmResult.isConfirmed) {
        const token = localStorage.getItem("authToken");

        const response = await axios.delete(
          `${apiUrl_4000}/coating/delete-coating`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
            data: selectedCoatings.map((coating) => ({ Coating_CD: coating })), // ส่งข้อมูล Coating_CD
          }
        );

        if (response.status === 200) {
          Swal.fire({
            title: "Deleted!",
            text: `The selected Coating_CD have been deleted.`,
            icon: "success",
            confirmButtonText: "OK",
          });

          // อัปเดตข้อมูลใน state
          setData(
            data.filter((row) => !selectedCoatings.includes(row.Coating_CD))
          );
          setSelectedCoatings([]); // เคลียร์การเลือก
          document
            .querySelectorAll('input[type="checkbox"]')
            .forEach((checkbox) => {
              checkbox.checked = false; // รีเซ็ต checkbox ทั้งหมด
            });
        } else {
          Swal.fire({
            title: "Error",
            text: response.data.message || "An error occurred while deleting.",
            icon: "error",
            confirmButtonText: "OK",
          });
        }
      }
    } catch (error) {
      console.error("Error in handleDeleteClick:", error);
      Swal.fire({
        title: "Error",
        text: "Something went wrong while deleting.",
        icon: "error",
        confirmButtonText: "OK",
      });
    }
  };

  const handleF12Click = async () => {
    try {
      const confirmResult = await Swal.fire({
        title: "Confirm",
        html: isModified
          ? "The data has been edited. Do you want to go back to master 2?<br>ข้อมูลถูกแก้ไขต้องการกลับไปที่หน้า master 2 หรือไม่?<br>データは編集されました。master 2 に戻りますか？"
          : "Do you want to go back to master 2?<br>คุณต้องการกลับไปที่หน้า master 2 หรือไม่?<br>master 2 に戻りますか？",
        icon: "warning",
        showCancelButton: true,
        confirmButtonText: "Yes",
        cancelButtonText: "No",
      });

      if (confirmResult.isConfirmed) {
        setIsModified(false); // รีเซ็ตสถานะเมื่อผู้ใช้ยืนยันปิด
        navigate("/master2");
      }
    } catch (error) {
      console.error("Error in handleF12Click:", error);
      Swal.fire({
        title: "Error",
        text: "Try again.",
        icon: "error",
        confirmButtonText: "OK",
      });
    }
  };

  const filteredData = data.filter((row) => {
    return Object.values(row).some((value) =>
      String(value).toLowerCase().includes(searchTerm.toLowerCase())
    );
  });

  // ฟังก์ชันสำหรับ Export ข้อมูลเป็น CSV
  const exportToCsv = () => {
    const csvData = data.map((row) => ({
      Coating_CD: row.Coating_CD,
      Coating_Name: row.Coating_Name,
      Coating_Abb: row.Coating_Abb,
      Coating_Symbol: row.Coating_Symbol,
      Coating_Remark: row.Coating_Remark,
    }));

    const csv = Papa.unparse(csvData); // แปลง JSON เป็น CSV
    const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" });

    // ดาวน์โหลดไฟล์ CSV
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", "Coating_data.csv");
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const columns = [
    {
      name: "SelectForCopy",
      selector: (row) => (
        <input
          type="radio"
          name="row-radio"
          checked={
            selectedRowForCopy &&
            selectedRowForCopy.Coating_CD === row.Coating_CD
          }
          onChange={() => setSelectedRowForCopy(row)}
        />
      ),
      width: "150px",
      omit: false,
    },
    {
      name: "SelectForDelete",
      selector: (row) =>
        showCheckbox ? (
          <input
            type="checkbox"
            className="checkbox"
            onChange={(e) => handleCheckboxChange(e, row.Coating_CD)}
          />
        ) : null, // ซ่อน checkbox หาก showCheckbox เป็น false
      width: "150px",
      omit: !showCheckbox,
    },
    {
      name: "Coating_CD",
      selector: (row) => (
        <input
          className="w-full p-2 bg-transparent"
          type="text"
          value={
            editedData[row.Coating_CD]?.Coating_CD !== undefined
              ? editedData[row.Coating_CD]?.Coating_CD
              : row.Coating_CD || ""
          }
          onChange={(e) => handleChange(e, row.Coating_CD, "Coating_CD")}
          disabled
        />
      ),
      width: "170px",
      sortable: true,
      sortFunction: (rowA, rowB) => {
        const valA =
          editedData[rowA.Coating_CD]?.Coating_CD || rowA.Coating_CD || "";
        const valB =
          editedData[rowB.Coating_CD]?.Coating_CD || rowB.Coating_CD || "";

        return valA.localeCompare(valB);
      },
    },
    {
      name: "Coating_Name",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Coating_CD]?.Coating_Name !== undefined
              ? editedData[row.Coating_CD]?.Coating_Name
              : row.Coating_Name || ""
          }
          onChange={(e) => handleChange(e, row.Coating_CD, "Coating_Name")}
          disabled={!isF2Pressed}
        />
      ),
      width: "190px",
    },
    {
      name: "Coating_Abb",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Coating_CD]?.Coating_Abb !== undefined
              ? editedData[row.Coating_CD]?.Coating_Abb
              : row.Coating_Abb || ""
          }
          onChange={(e) => handleChange(e, row.Coating_CD, "Coating_Abb")}
          disabled={!isF2Pressed}
        />
      ),
      width: "190px",
    },
    {
      name: "Coating_Symbol",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Coating_CD]?.Coating_Symbol !== undefined
              ? editedData[row.Coating_CD]?.Coating_Symbol
              : row.Coating_Symbol || ""
          }
          onChange={(e) => handleChange(e, row.Coating_CD, "Coating_Symbol")}
          disabled={!isF2Pressed}
        />
      ),
      width: "190px",
    },
    {
      name: "Coating_Remark",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Coating_CD]?.Coating_Remark !== undefined
              ? editedData[row.Coating_CD]?.Coating_Remark
              : row.Coating_Remark || ""
          }
          onChange={(e) => handleChange(e, row.Coating_CD, "Coating_Remark")}
          disabled={!isF2Pressed}
        />
      ),
      width: "190px",
    },
  ];

  return (
    <div className="flex bg-[#E9EFEC] h-[100vh]">
      <Sidebar />
      <div className="flex flex-col w-full mr-2 ml-2">
        <Navbar />
        <div className="flex-1 flex-col p-2 overflow-x-auto">
          <div className="bg-white grid grid-cols-1">
            <div className="bg-white grid grid-cols-1">
              <h1 className="text-2xl font-bold text-center mt-3">
                Coating <br /> （CVD･PVD･DLC）
              </h1>
              <hr className="my-6 h-0.5 bg-gray-500 opacity-100 dark:opacity-50 border-y-[1px] border-gray-300" />

              <div className="ml-5 text-lg flex justify-between">
                <input
                  className="border-2 border-gray-500 rounded-md w-36 sm:w-52 h-9"
                  type="text"
                  placeholder=" Search..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
                <button
                  onClick={exportToCsv}
                  className="bg-blue-500 text-white px-4 py-2 rounded-md mr-5"
                >
                  Export to CSV
                </button>
              </div>

              <div className="flex justify-left items-center mt-5 mb-3">
                <div className="w-full sm:w-auto text-center px-5">
                  <DataTable
                    columns={columns}
                    data={filteredData}
                    pagination
                    paginationPerPage={10}
                    paginationRowsPerPageOptions={[10, 15, 20, 25, 30]}
                    customStyles={{
                      rows: {
                        style: {
                          "&:nth-of-type(odd)": { backgroundColor: "#ffffff" },
                          "&:nth-of-type(even)": { backgroundColor: "#F5F5F5" },
                          minHeight: "50px",
                          textAlign: "center",
                          justifyContent: "center",
                          borderBottom: "1px solid #ccc",
                          borderRight: "1px solid #ccc",
                        },
                      },
                      headCells: {
                        style: {
                          backgroundColor: "#DCDCDC",
                          fontSize: "14px",
                          textAlign: "center",
                          justifyContent: "center",
                          border: "1px solid #ccc",
                        },
                      },
                      cells: {
                        style: {
                          textAlign: "center",
                          justifyContent: "center",
                          border: "1px solid #ccc",
                        },
                      },
                      table: {
                        style: {
                          borderCollapse: "collapse",
                        },
                      },
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="bg-white p-3 mt-5">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-6 lg:grid-cols-12 gap-4">
            {/* First button group */}
            <button
              id="F1"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              disabled
            >
              (F1)
            </button>
            <button
              id="F2"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={() => {
                setIsF2Pressed(true); // เปลี่ยนสถานะเมื่อกด F2
              }}
              disabled={isF2Pressed}
            >
              Edit <br />
              編集 (F2)
            </button>
            <button
              id="F3"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={openModal}
              disabled={isF2Pressed}
            >
              New Add <br />
              新規追加 (F3)
            </button>

            {/* Modal */}
            {isModalOpen && (
              <div className="fixed inset-0 flex justify-center items-center bg-gray-500 bg-opacity-50">
                <div
                  className="bg-white p-6 rounded-lg w-1/2 max-h-[80vh] overflow-y-auto"
                  onClick={(e) => e.stopPropagation()}
                >
                  <h2 className="text-xl mb-4">Add New Coating</h2>
                  <form onSubmit={handleSaveCoating}>
                    {/* Coating_CD */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Coating_CD
                      </label>
                      <input
                        type="text"
                        className="w-full p-2 border rounded-md"
                        maxLength="2" // จำกัดความยาวไม่เกิน 2 ตัวอักษร
                        value={formData.Coating_CD || ""}
                        onChange={(e) =>
                          setFormData({
                            ...formData,
                            Coating_CD: e.target.value,
                          })
                        }
                        required // ทำให้เป็นฟิลด์บังคับ
                      />
                    </div>

                    {/* Coating_Name */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Coating_Name
                      </label>
                      <input
                        type="text"
                        className="w-full p-2 border rounded-md"
                        maxLength="10" // จำกัดความยาวไม่เกิน 10 ตัวอักษร
                        value={formData.Coating_Name || ""}
                        onChange={(e) =>
                          setFormData({
                            ...formData,
                            Coating_Name: e.target.value,
                          })
                        }
                      />
                    </div>

                    {/* Coating_Abb */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Coating_Abb
                      </label>
                      <input
                        type="text"
                        className="w-full p-2 border rounded-md"
                        maxLength="5" // จำกัดความยาวไม่เกิน 5 ตัวอักษร
                        value={formData.Coating_Abb || ""}
                        onChange={(e) =>
                          setFormData({
                            ...formData,
                            Coating_Abb: e.target.value,
                          })
                        }
                      />
                    </div>

                    {/* Coating_Symbol */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Coating_Symbol
                      </label>
                      <input
                        type="text"
                        className="w-full p-2 border rounded-md"
                        maxLength="3" // จำกัดความยาวไม่เกิน 3 ตัวอักษร
                        value={formData.Coating_Symbol || ""}
                        onChange={(e) =>
                          setFormData({
                            ...formData,
                            Coating_Symbol: e.target.value,
                          })
                        }
                      />
                    </div>

                    {/* Coating_Remark */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Coating_Remark
                      </label>
                      <textarea
                        className="w-full p-2 border rounded-md h-24 overflow-y-auto"
                        maxLength="20" // จำกัดความยาวไม่เกิน 20 ตัวอักษร
                        value={formData.Coating_Remark || ""}
                        onChange={(e) =>
                          setFormData({
                            ...formData,
                            Coating_Remark: e.target.value,
                          })
                        }
                      />
                    </div>

                    {/* Save and Close Buttons */}
                    <div className="mt-4 flex justify-end gap-4">
                      <button
                        type="submit"
                        className="bg-blue-500 p-3 rounded-lg text-white"
                      >
                        Save
                      </button>
                      <button
                        type="button"
                        onClick={closeModal}
                        className="bg-gray-500 p-3 rounded-lg text-white"
                      >
                        Close
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            )}

            <button
              id="F4"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={handleSelectClick}
              disabled={isF2Pressed}
            >
              Select <br />
              選択 (F4)
            </button>

            <button
              id="F5"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              disabled
            >
              (F5)
            </button>
            <button
              id="F6"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              disabled
            >
              (F6)
            </button>
            <button
              id="F7"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              disabled
            >
              (F7)
            </button>
            <button
              id="F8"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              disabled
            >
              (F8)
            </button>
            <button
              id="F9"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={() => {
                data.forEach((row) => {
                  Object.keys(row).forEach((field) => {
                    if (editedData[row.Coating_CD]?.[field] !== undefined) {
                      handleSave(row.Coating_CD, field);
                    }
                  });
                });
              }}
              disabled={!isF2Pressed} // จะเปิดใช้งาน F9 เมื่อกด F2 แล้ว
            >
              Save <br />
              保存 (F9)
            </button>
            <button
              id="F10"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={handleDeleteClick}
              disabled={isButtonDisabled} // ตั้งค่าปุ่มให้ disabled ตามสถานะ
            >
              Delete <br />
              消去 (F10)
            </button>
            <button
              id="F11"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={() => {
                window.location.reload(); // รีเฟรชหน้า
              }}
              disabled={false} // ตั้งค่า disabled เป็น false ถ้าคุณต้องการให้ปุ่มทำงาน
            >
              Clear <br />
              クリア (F11)
            </button>

            <button
              id="F12"
              onClick={handleF12Click}
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
            >
              Back <br />
              戻る (F12)
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
