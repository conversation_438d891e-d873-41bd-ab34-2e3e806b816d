import React, { useState, useEffect } from "react";
import Navbar from "../Navbar";
import Sidebar from "../Sidebar";
import DataTable from "react-data-table-component";
import axios from "axios";
import Papa from "papaparse";
import { useNavigate } from "react-router-dom";
import Swal from "sweetalert2";

export function Process() {
  const apiUrl_4000 = import.meta.env.VITE_BACKEND_URL;

  const [isF2Pressed, setIsF2Pressed] = useState(false);
  const [isModified, setIsModified] = useState(false);
  const navigate = useNavigate();
  const [showCheckbox, setShowCheckbox] = useState(false);
  const [isButtonDisabled, setIsButtonDisabled] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [data, setData] = useState([]);
  const [selectedProcess, setSelectedProcess] = useState([]);
  const [editedData, setEditedData] = useState({});
  const [selectedRowForCopy, setSelectedRowForCopy] = useState(null); // For radio selection

  const fetchAllProcess = async () => {
    try {
      const response = await axios.get(
        `${apiUrl_4000}/process/fetch-all-process`
      );
      // console.log("Fetched data:", response.data);
      setData(response.data.data.process || []);
    } catch (error) {
      // console.error("Error fetching process:", error);
    }
  };

  useEffect(() => {
    fetchAllProcess();
  }, []);

  const [formData, setFormData] = useState({
    Process_CD: "",
    Change_CD: "",
    ProcessG_CD: "",
    ResourceG_CD: "",
    ManageG_CD: "",
    Manhour_Calc: "",
    Days_Calc: "",
    Process_Name: "",
    Process_Abb: "",
    Process_Symbol: "",
    Process_Mark: "",
    Use: false,
    For_Plan: false,
    For_Info: false,
    Graph: false,
    List: false,
    Outside_On: false,
    Outside_Off: false,
    End: false,
    Coefficient: "",
    M_Coefficient: "",
    P_Coefficient: "",
    Before: "",
    After: "",
    Operation_Time: "",
    Std_M_CAT: "",
    Std_M_Time: "",
    Std_P_CAT: "",
    Std_P_Time: "",
    T_Type: "",
    P_Type: "",
    S_Type: "",
    Process_Remark: "",
  });

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleChange = (e, processCd, field) => {
    setEditedData({
      ...editedData,
      [processCd]: {
        ...editedData[processCd],
        [field]: e.target.value,
      },
    });
  };

  const openModal = () => {
    if (selectedRowForCopy) {
      const { Process_CD, ...rest } = selectedRowForCopy;
      setFormData({
        Process_CD: "",
        ...rest,
      });
    } else {
      setFormData({
        Process_CD: "",
        Change_CD: "",
        ProcessG_CD: "",
        ResourceG_CD: "",
        ManageG_CD: "",
        Manhour_Calc: "",
        Days_Calc: "",
        Process_Name: "",
        Process_Abb: "",
        Process_Symbol: "",
        Process_Mark: "",
        Use: false,
        For_Plan: false,
        For_Info: false,
        Graph: false,
        List: false,
        Outside_On: false,
        Outside_Off: false,
        End: false,
        Coefficient: "",
        M_Coefficient: "",
        P_Coefficient: "",
        Before: "",
        After: "",
        Operation_Time: "",
        Std_M_CAT: "",
        Std_M_Time: "",
        Std_P_CAT: "",
        Std_P_Time: "",
        T_Type: "",
        P_Type: "",
        S_Type: "",
        Process_Remark: "",
      });
    }
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  const handleSelectClick = () => {
    document.getElementById("F1").disabled = true;
    document.getElementById("F2").disabled = true;
    document.getElementById("F3").disabled = true;
    document.getElementById("F4").disabled = true;
    document.getElementById("F5").disabled = true;
    document.getElementById("F6").disabled = true;
    document.getElementById("F7").disabled = true;
    document.getElementById("F8").disabled = true;
    document.getElementById("F9").disabled = true;
    document.getElementById("F10").disabled = false;
    document.getElementById("F11").disabled = false;
    document.getElementById("F12").disabled = false;

    setIsButtonDisabled(false);
    handleCheckboxToggle();
  };

  const handleCheckboxChange = (event, processCd) => {
    const isChecked = event.target.checked;

    setSelectedProcess((prev) => {
      if (isChecked) {
        return [...prev, processCd];
      } else {
        return prev.filter((cd) => cd !== processCd);
      }
    });
  };

  const handleCheckboxChangeForEdit = (e, processCd, field) => {
    const { checked } = e.target;

    // อัปเดตข้อมูลใน editedData หรือ state ของ row
    setEditedData((prevData) => ({
      ...prevData,
      [processCd]: {
        ...prevData[processCd],
        [field]: checked,
      },
    }));
  };

  const handleCreateProcess = async (e) => {
    e.preventDefault();
    // เงื่อนไขตรวจสอบ T_Type, P_Type, S_Type
    if (
      (formData.T_Type || "").length > 1 ||
      (formData.P_Type || "").length > 1 ||
      (formData.S_Type || "").length > 1 ||
      (!/^[A-Za-z]*$/.test(formData.T_Type || "") &&
        (formData.T_Type || "") !== "") ||
      (!/^[A-Za-z]*$/.test(formData.P_Type || "") &&
        (formData.P_Type || "") !== "") ||
      (!/^[A-Za-z]*$/.test(formData.S_Type || "") &&
        (formData.S_Type || "") !== "")
    ) {
      Swal.fire({
        title: "Validation Error",
        text: "T_Type,P_Type and S_Type must be a single character (A-Z, a-z) or (0-9).",
        icon: "error",
        confirmButtonText: "OK",
      });
      return;
    }

    // เงื่อนไขตรวจสอบ Manhour_Calc และ Days_Calc
    if (
      (formData.Manhour_Calc || "").length > 2 ||
      (formData.Days_Calc || "").length > 2 ||
      (!/^[A-Za-z0-9]*$/.test(formData.Manhour_Calc || "") &&
        formData.Manhour_Calc !== "") ||
      (!/^[A-Za-z0-9]*$/.test(formData.Days_Calc || "") &&
        formData.Days_Calc !== "")
    ) {
      Swal.fire({
        title: "Validation Error",
        text: "Manhour_Calc and Days_Calc must be up to 2 characters only (A-Z, a-z, 0-9).",
        icon: "error",
        confirmButtonText: "OK",
      });
      return;
    }

    // เงื่อนไขตรวจสอบ Std_M_CAT และ Std_P_CAT
    if (
      (formData.Std_M_CAT || "") > 100 ||
      (formData.Std_P_CAT || "") > 100 ||
      !/^[0-9]*$/.test(formData.Std_M_CAT || "") ||
      !/^[0-9]*$/.test(formData.Std_P_CAT || "")
    ) {
      Swal.fire({
        title: "Validation Error",
        text: "Std_M_CAT and Std_P_CAT must not exceed 100.",
        icon: "error",
        confirmButtonText: "OK",
      });
      return;
    }

    try {
      const token = localStorage.getItem("authToken");

      const response = await axios.post(
        `${apiUrl_4000}/process/create-process`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (response.status === 200) {
        Swal.fire({
          title: "Success",
          text: "Process created successfully.",
          icon: "success",
          confirmButtonText: "OK",
        });

        // อัปเดตข้อมูลในตารางหลังจากสร้างสำเร็จ
        fetchAllProcess();

        // ล้างค่าฟอร์ม
        setFormData({
          Process_CD: "",
          Change_CD: "",
          ProcessG_CD: "",
          ResourceG_CD: "",
          ManageG_CD: "",
          Manhour_Calc: "",
          Days_Calc: "",
          Process_Name: "",
          Process_Abb: "",
          Process_Symbol: "",
          Process_Mark: "",
          Use: false,
          For_Plan: false,
          For_Info: false,
          Graph: false,
          List: false,
          Outside_On: false,
          Outside_Off: false,
          End: false,
          Coefficient: "",
          M_Coefficient: "",
          P_Coefficient: "",
          Before: "",
          After: "",
          Operation_Time: "",
          Std_M_CAT: "",
          Std_M_Time: "",
          Std_P_CAT: "",
          Std_P_Time: "",
          T_Type: "",
          P_Type: "",
          S_Type: "",
          Process_Remark: "",
        });

        // Clear the selected radio button by setting selectedRowForCopy to null
        setSelectedRowForCopy(null);

        // ปิด Modal
        closeModal();
      }
    } catch (error) {
      console.error("Error creating process:", error);
      Swal.fire({
        title: "Error",
        text: error.response?.data?.message || "Failed to create process.",
        icon: "error",
        confirmButtonText: "OK",
      });
    }
  };

  const handleUpdateProcess = async (processCd) => {
    const payload = { Process_CD: processCd };
    let isEdited = false;

    const editedFields = Object.keys(editedData[processCd] || {});

    for (const field of editedFields) {
      const newValue = editedData[processCd]?.[field];
      const oldValue = data.find((row) => row.Process_CD === processCd)?.[
        field
      ];

      if (newValue !== oldValue) {
        // ตรวจสอบฟิลด์ Change_CD
        if (
          field === "Change_CD" &&
          (!newValue || !/^[A-Za-z0-9]*$/.test(newValue)) // ตรวจสอบค่าว่างและให้เป็นตัวอักษรหรือตัวเลข
        ) {
          Swal.fire({
            title: "Validation Error",
            text: `${field} cannot be blank and must be a valid string or number.`,
            icon: "error",
            confirmButtonText: "OK",
          });
          return;
        }

        // ตรวจสอบฟิลด์ T_Type, P_Type, S_Type
        if (
          (field === "T_Type" || field === "P_Type" || field === "S_Type") &&
          (newValue.length > 1 ||
            (!/^[A-Za-z]*$/.test(newValue) && newValue !== ""))
        ) {
          Swal.fire({
            title: "Validation Error",
            text: `${field} must be a single character (A-Z, a-z) or (0-9).`,
            icon: "error",
            confirmButtonText: "OK",
          });
          return;
        }

        // ตรวจสอบฟิลด์ Manhour_Calc และ Days_Calc
        if (
          (field === "Manhour_Calc" || field === "Days_Calc") &&
          (newValue.length > 2 ||
            (!/^[A-Za-z0-9]*$/.test(newValue) && newValue !== ""))
        ) {
          Swal.fire({
            title: "Validation Error",
            text: `${field} must be up to 2 characters only (A-Z, a-z, 0-9).`,
            icon: "error",
            confirmButtonText: "OK",
          });
          return;
        }

        // ตรวจสอบฟิลด์ Std_M_CAT และ Std_P_CAT
        if (
          (field === "Std_M_CAT" || field === "Std_P_CAT") &&
          (newValue > 100 || newValue.length > 3 || !/^[0-9]*$/.test(newValue))
        ) {
          Swal.fire({
            title: "Validation Error",
            text: `${field} must not exceed 100.`,
            icon: "error",
            confirmButtonText: "OK",
          });
          return;
        }

        payload[field] = newValue === "" ? null : newValue;
        isEdited = true;
      }
    }

    if (!isEdited) {
      Swal.fire({
        icon: "info",
        title: "No changes made!",
        text: "Please make sure to edit something before saving.",
      });
      return;
    }

    let isSaving = false;

    if (isSaving) {
      return;
    }

    try {
      isSaving = true;

      const response = await axios.put(
        `${apiUrl_4000}/process/update-process`,
        payload
      );

      const updatedData = [...data];
      const rowIndex = updatedData.findIndex(
        (row) => row.Process_CD === processCd
      );
      if (rowIndex !== -1) {
        Object.keys(payload).forEach((field) => {
          if (field !== "Process_CD ") {
            updatedData[rowIndex][field] = payload[field];
          }
        });
        setData(updatedData);
      }

      Swal.fire({
        icon: "success",
        title: "Edit Successfully!",
        text: "Process data has been updated.",
      });
    } catch (error) {
      Swal.fire({
        icon: "error",
        title: "Something went wrong!",
        text: "Please try again later.",
      });
      console.error(error);
    } finally {
      isSaving = false;
    }
  };

  const handleDeleteProcess = async () => {
    if (selectedProcess.length === 0) {
      Swal.fire({
        title: "No Selection",
        text: "Please select at least one item to delete.",
        icon: "warning",
        confirmButtonText: "OK",
      });
      return;
    }

    const processList = selectedProcess.join(", "); // รวม Process_CD ที่เลือกเป็น String

    try {
      const confirmResult = await Swal.fire({
        title: "Confirm Delete",
        html: `Are you sure you want to delete the following items?<br>Process CDs: <b>${processList}</b>`,
        icon: "warning",
        showCancelButton: true,
        confirmButtonText: "Yes",
        cancelButtonText: "No",
      });

      if (confirmResult.isConfirmed) {
        const token = localStorage.getItem("authToken");

        const response = await axios.delete(
          `${apiUrl_4000}/process/delete-process`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
            data: selectedProcess.map((cd) => ({ Process_CD: cd })), // ส่งเป็นอาเรย์ของ { Process_CD }
          }
        );

        if (response.status === 200) {
          Swal.fire({
            title: "Deleted!",
            text: "The selected process have been deleted.",
            icon: "success",
            confirmButtonText: "OK",
          });

          // รีเฟรชข้อมูล
          fetchAllProcess();

          setSelectedProcess([]);
        } else {
          Swal.fire({
            title: "Error",
            text: response.data.message || "An error occurred while deleting.",
            icon: "error",
            confirmButtonText: "OK",
          });
        }
      }
    } catch (error) {
      console.error("Error in handleDeleteProcess:", error);
      Swal.fire({
        title: "Error",
        text: "Something went wrong while deleting.",
        icon: "error",
        confirmButtonText: "OK",
      });
    }
  };

  useEffect(() => {
    const initialEditedData = data.reduce((acc, row, index) => {
      if (!editedData[index]) {
        acc[index] = { ...row };
      }
      return acc;
    }, {});

    if (Object.keys(initialEditedData).length > 0) {
      setEditedData(initialEditedData);
    }
  }, [data]);

  const filteredData = data.filter((row) => {
    return Object.values(row).some((value) =>
      String(value).toLowerCase().includes(searchTerm.toLowerCase())
    );
  });

  const handleF12Click = async () => {
    try {
      const confirmResult = await Swal.fire({
        title: "Confirm",
        html: isModified
          ? "The data has been edited. Do you want to go back to master 3?<br>ข้อมูลถูกแก้ไขต้องการกลับไปที่หน้า master 3 หรือไม่?<br>データは編集されました。master 3 に戻りますか？"
          : "Do you want to go back to master 3?<br>คุณต้องการกลับไปที่หน้า master 3 หรือไม่?<br>master 3 に戻りますか？",
        icon: "warning",
        showCancelButton: true,
        confirmButtonText: "Yes",
        cancelButtonText: "No",
      });

      if (confirmResult.isConfirmed) {
        setIsModified(false);
        navigate("/master3");
      }
    } catch (error) {
      console.error("Error in handleF12Click:", error);
      Swal.fire({
        title: "Error",
        text: "Try again.",
        icon: "error",
        confirmButtonText: "OK",
      });
    }
  };

  // ฟังก์ชันสำหรับ Export ข้อมูลเป็น CSV
  const exportToCsv = () => {
    const csvData = data.map((row) => ({
      Process_CD: row.Process_CD,
      Change_CD: row.Change_CD,
      ProcessG_CD: row.ProcessG_CD,
      ResourceG_CD: row.ResourceG_CD,
      ManageG_CD: row.ManageG_CD,
      Manhour_Calc: row.Manhour_Calc,
      Days_Calc: row.Days_Calc,
      Process_Name: row.Process_Name,
      Process_Abb: row.Process_Abb,
      Process_Symbol: row.Process_Symbol,
      Process_Mark: row.Process_Mark,
      Use: row.Use,
      For_Plan: row.For_Plan,
      For_Info: row.For_Info,
      Graph: row.Graph,
      List: row.List,
      Outside_On: row.Outside_On,
      Outside_Off: row.Outside_Off,
      End: row.End,
      Coefficient: row.Coefficient,
      M_Coefficient: row.M_Coefficient,
      P_Coefficient: row.P_Coefficient,
      Before: row.Before,
      After: row.After,
      Operation_Time: row.Operation_Time,
      Std_M_CAT: row.Std_M_CAT,
      Std_M_Time: row.Std_M_Time,
      Std_P_CAT: row.Std_P_CAT,
      Std_P_Time: row.Std_P_Time,
      T_Type: row.T_Type,
      P_Type: row.P_Type,
      S_Type: row.S_Type,
      Process_Remark: row.Process_Remark,
    }));

    const csv = Papa.unparse(csvData); // แปลง JSON เป็น CSV
    const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" });

    // ดาวน์โหลดไฟล์ CSV
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", "Process_data.csv");
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleCheckboxToggle = () => {
    setShowCheckbox(!showCheckbox);
  };

  const columns = [
    {
      name: "SelectForCopy",
      selector: (row) => (
        <input
          type="radio"
          name="row-radio"
          checked={
            selectedRowForCopy &&
            selectedRowForCopy.Process_CD === row.Process_CD
          }
          onChange={() => setSelectedRowForCopy(row)}
        />
      ),
      width: "150px",
      omit: false,
    },
    {
      name: "SelectForDelete",
      selector: (row) =>
        showCheckbox ? (
          <input
            type="checkbox"
            className="checkbox"
            checked={selectedProcess.includes(row.Process_CD)}
            onChange={(e) => handleCheckboxChange(e, row.Process_CD)}
          />
        ) : null, // ซ่อน checkbox หาก showCheckbox เป็น false
      width: "150px",
      omit: !showCheckbox,
    },
    {
      name: "Process_CD",
      selector: (row) => (
        <input
          className="w-full p-2 bg-transparent"
          type="text"
          value={
            editedData[row.Process_CD]?.Process_CD !== undefined
              ? editedData[row.Process_CD]?.Process_CD
              : row.Process_CD || ""
          }
          onChange={(e) => handleChange(e, row.Process_CD, "Process_CD")}
          disabled
        />
      ),
      width: "190px",
      sortable: true,
      sortFunction: (rowA, rowB) => {
        const valA =
          editedData[rowA.Process_CD]?.Process_CD || rowA.Process_CD || "";
        const valB =
          editedData[rowB.Process_CD]?.Process_CD || rowB.Process_CD || "";

        return valA.localeCompare(valB);
      },
    },
    {
      name: "Change_CD",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Process_CD]?.Change_CD !== undefined
              ? editedData[row.Process_CD]?.Change_CD
              : row.Change_CD || ""
          }
          onChange={(e) => handleChange(e, row.Process_CD, "Change_CD")}
          disabled={!isF2Pressed}
        />
      ),
      width: "190px",
    },
    {
      name: "ProcessG_CD",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Process_CD]?.ProcessG_CD !== undefined
              ? editedData[row.Process_CD]?.ProcessG_CD
              : row.ProcessG_CD || ""
          }
          onChange={(e) => handleChange(e, row.Process_CD, "ProcessG_CD")}
          disabled
        />
      ),
      width: "190px",
    },
    {
      name: "ResourceG_CD",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Process_CD]?.ResourceG_CD !== undefined
              ? editedData[row.Process_CD]?.ResourceG_CD
              : row.ResourceG_CD || ""
          }
          onChange={(e) => handleChange(e, row.Process_CD, "ResourceG_CD")}
          disabled
        />
      ),
      width: "190px",
    },
    {
      name: "ManageG_CD",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Process_CD]?.ManageG_CD !== undefined
              ? editedData[row.Process_CD]?.ManageG_CD
              : row.ManageG_CD || ""
          }
          onChange={(e) => handleChange(e, row.Process_CD, "ManageG_CD")}
          disabled={!isF2Pressed}
        />
      ),
      width: "190px",
    },
    {
      name: "Manhour_Calc",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Process_CD]?.Manhour_Calc !== undefined
              ? editedData[row.Process_CD]?.Manhour_Calc
              : row.Manhour_Calc || ""
          }
          onChange={(e) => handleChange(e, row.Process_CD, "Manhour_Calc")}
          disabled={!isF2Pressed}
        />
      ),
      width: "190px",
    },
    {
      name: "Days_Calc",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Process_CD]?.Days_Calc !== undefined
              ? editedData[row.Process_CD]?.Days_Calc
              : row.Days_Calc || ""
          }
          onChange={(e) => handleChange(e, row.Process_CD, "Days_Calc")}
          disabled={!isF2Pressed}
        />
      ),
      width: "190px",
    },
    {
      name: "Process_Name",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Process_CD]?.Process_Name !== undefined
              ? editedData[row.Process_CD]?.Process_Name
              : row.Process_Name || ""
          }
          onChange={(e) => handleChange(e, row.Process_CD, "Process_Name")}
          disabled={!isF2Pressed}
        />
      ),
      width: "190px",
    },
    {
      name: "Process_Abb",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Process_CD]?.Process_Abb !== undefined
              ? editedData[row.Process_CD]?.Process_Abb
              : row.Process_Abb || ""
          }
          onChange={(e) => handleChange(e, row.Process_CD, "Process_Abb")}
          disabled={!isF2Pressed}
        />
      ),
      width: "190px",
    },
    {
      name: "Process_Symbol",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Process_CD]?.Process_Symbol !== undefined
              ? editedData[row.Process_CD]?.Process_Symbol
              : row.Process_Symbol || ""
          }
          onChange={(e) => handleChange(e, row.Process_CD, "Process_Symbol")}
          disabled={!isF2Pressed}
        />
      ),
      width: "190px",
    },
    {
      name: "Process_Mark",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Process_CD]?.Process_Mark !== undefined
              ? editedData[row.Process_CD]?.Process_Mark
              : row.Process_Mark || ""
          }
          onChange={(e) => handleChange(e, row.Process_CD, "Process_Mark")}
          disabled={!isF2Pressed}
        />
      ),
      width: "190px",
    },
    {
      name: "Use",
      selector: (row) => (
        <input
          type="checkbox"
          checked={editedData[row.Process_CD]?.Use ?? row.Use}
          className="mx-auto"
          onChange={(e) =>
            handleCheckboxChangeForEdit(e, row.Process_CD, "Use")
          }
          disabled={!isF2Pressed}
        />
      ),
      width: "150px",
    },
    {
      name: "For_Plan",
      selector: (row) => (
        <input
          type="checkbox"
          checked={editedData[row.Process_CD]?.For_Plan ?? row.For_Plan}
          className="mx-auto"
          onChange={(e) =>
            handleCheckboxChangeForEdit(e, row.Process_CD, "For_Plan")
          }
          disabled={!isF2Pressed}
        />
      ),
      width: "150px",
    },
    {
      name: "For_Info",
      selector: (row) => (
        <input
          type="checkbox"
          checked={editedData[row.Process_CD]?.For_Info ?? row.For_Info}
          className="mx-auto"
          onChange={(e) =>
            handleCheckboxChangeForEdit(e, row.Process_CD, "For_Info")
          }
          disabled={!isF2Pressed}
        />
      ),
      width: "150px",
    },
    {
      name: "Graph",
      selector: (row) => (
        <input
          type="checkbox"
          checked={editedData[row.Process_CD]?.Graph ?? row.Graph}
          className="mx-auto"
          onChange={(e) =>
            handleCheckboxChangeForEdit(e, row.Process_CD, "Graph")
          }
          disabled={!isF2Pressed}
        />
      ),
      width: "150px",
    },
    {
      name: "List",
      selector: (row) => (
        <input
          type="checkbox"
          checked={editedData[row.Process_CD]?.List ?? row.List}
          className="mx-auto"
          onChange={(e) =>
            handleCheckboxChangeForEdit(e, row.Process_CD, "List")
          }
          disabled={!isF2Pressed}
        />
      ),
      width: "150px",
    },
    {
      name: "Outside_On",
      selector: (row) => (
        <input
          type="checkbox"
          checked={editedData[row.Process_CD]?.Outside_On ?? row.Outside_On}
          className="mx-auto"
          onChange={(e) =>
            handleCheckboxChangeForEdit(e, row.Process_CD, "Outside_On")
          }
          disabled={!isF2Pressed}
        />
      ),
      width: "150px",
    },
    {
      name: "Outside_Off",
      selector: (row) => (
        <input
          type="checkbox"
          checked={editedData[row.Process_CD]?.Outside_Off ?? row.Outside_Off}
          className="mx-auto"
          onChange={(e) =>
            handleCheckboxChangeForEdit(e, row.Process_CD, "Outside_Off")
          }
          disabled={!isF2Pressed}
        />
      ),
      width: "150px",
    },
    {
      name: "End",
      selector: (row) => (
        <input
          type="checkbox"
          checked={editedData[row.Process_CD]?.End ?? row.End}
          className="mx-auto"
          onChange={(e) =>
            handleCheckboxChangeForEdit(e, row.Process_CD, "End")
          }
          disabled={!isF2Pressed}
        />
      ),
      width: "150px",
    },
    {
      name: "Coefficient",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="number"
          value={
            editedData[row.Process_CD]?.Coefficient !== undefined
              ? editedData[row.Process_CD]?.Coefficient
              : row.Coefficient ?? ""
          }
          onChange={(e) => handleChange(e, row.Process_CD, "Coefficient")}
          disabled={!isF2Pressed}
        />
      ),
      width: "180px",
    },
    {
      name: "M_Coefficient",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="number"
          value={
            editedData[row.Process_CD]?.M_Coefficient !== undefined
              ? editedData[row.Process_CD]?.M_Coefficient
              : row.M_Coefficient ?? ""
          }
          onChange={(e) => handleChange(e, row.Process_CD, "M_Coefficient")}
          disabled={!isF2Pressed}
        />
      ),
      width: "180px",
    },
    {
      name: "P_Coefficient",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="number"
          value={
            editedData[row.Process_CD]?.P_Coefficient !== undefined
              ? editedData[row.Process_CD]?.P_Coefficient
              : row.P_Coefficient ?? ""
          }
          onChange={(e) => handleChange(e, row.Process_CD, "P_Coefficient")}
          disabled={!isF2Pressed}
        />
      ),
      width: "180px",
    },
    {
      name: "Before",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="number"
          value={
            editedData[row.Process_CD]?.Before !== undefined
              ? editedData[row.Process_CD]?.Before
              : row.Before ?? ""
          }
          onChange={(e) => handleChange(e, row.Process_CD, "Before")}
          disabled={!isF2Pressed}
        />
      ),
      width: "180px",
    },
    {
      name: "After",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="number"
          value={
            editedData[row.Process_CD]?.After !== undefined
              ? editedData[row.Process_CD]?.After
              : row.After ?? ""
          }
          onChange={(e) => handleChange(e, row.Process_CD, "After")}
          disabled={!isF2Pressed}
        />
      ),
      width: "180px",
    },
    {
      name: "Operation_Time",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="number"
          value={
            editedData[row.Process_CD]?.Operation_Time !== undefined
              ? editedData[row.Process_CD]?.Operation_Time
              : row.Operation_Time ?? ""
          }
          onChange={(e) => handleChange(e, row.Process_CD, "Operation_Time")}
          disabled={!isF2Pressed}
        />
      ),
      width: "180px",
    },
    {
      name: "Std_M_CAT",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="number"
          value={
            editedData[row.Process_CD]?.Std_M_CAT !== undefined
              ? editedData[row.Process_CD]?.Std_M_CAT
              : row.Std_M_CAT ?? ""
          }
          onChange={(e) => handleChange(e, row.Process_CD, "Std_M_CAT")}
          disabled={!isF2Pressed}
        />
      ),
      width: "180px",
    },
    {
      name: "Std_M_Time",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="number"
          value={
            editedData[row.Process_CD]?.Std_M_Time !== undefined
              ? editedData[row.Process_CD]?.Std_M_Time
              : row.Std_M_Time ?? ""
          }
          onChange={(e) => handleChange(e, row.Process_CD, "Std_M_Time")}
          disabled={!isF2Pressed}
        />
      ),
      width: "180px",
    },
    {
      name: "Std_P_CAT",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="number"
          value={
            editedData[row.Process_CD]?.Std_P_CAT !== undefined
              ? editedData[row.Process_CD]?.Std_P_CAT
              : row.Std_P_CAT ?? ""
          }
          onChange={(e) => handleChange(e, row.Process_CD, "Std_P_CAT")}
          disabled={!isF2Pressed}
        />
      ),
      width: "180px",
    },
    {
      name: "Std_P_Time",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="number"
          value={
            editedData[row.Process_CD]?.Std_P_Time !== undefined
              ? editedData[row.Process_CD]?.Std_P_Time
              : row.Std_P_Time ?? ""
          }
          onChange={(e) => handleChange(e, row.Process_CD, "Std_P_Time")}
          disabled={!isF2Pressed}
        />
      ),
      width: "180px",
    },
    {
      name: "T_Type",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Process_CD]?.T_Type !== undefined
              ? editedData[row.Process_CD]?.T_Type
              : row.T_Type || ""
          }
          onChange={(e) => handleChange(e, row.Process_CD, "T_Type")}
          disabled={!isF2Pressed}
        />
      ),
      width: "180px",
    },
    {
      name: "P_Type",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Process_CD]?.P_Type !== undefined
              ? editedData[row.Process_CD]?.P_Type
              : row.P_Type || ""
          }
          onChange={(e) => handleChange(e, row.Process_CD, "P_Type")}
          disabled={!isF2Pressed}
        />
      ),
      width: "180px",
    },
    {
      name: "S_Type",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Process_CD]?.S_Type !== undefined
              ? editedData[row.Process_CD]?.S_Type
              : row.S_Type || ""
          }
          onChange={(e) => handleChange(e, row.Process_CD, "S_Type")}
          disabled={!isF2Pressed}
        />
      ),
      width: "180px",
    },
    {
      name: "Process_Remark",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          style={{
            width: "fit-content",
            minWidth: "340px",
            maxWidth: "100%",
          }}
          value={
            editedData[row.Process_CD]?.Process_Remark !== undefined
              ? editedData[row.Process_CD]?.Process_Remark
              : row.Process_Remark || ""
          }
          onChange={(e) => handleChange(e, row.Process_CD, "Process_Remark")}
          disabled={!isF2Pressed}
        />
      ),
      width: "400px",
    },
  ];

  return (
    <div className="flex bg-[#E9EFEC] h-[100vh]">
      <Sidebar />
      <div className="flex flex-col w-full mr-2 ml-2">
        <Navbar />
        <div className="flex-1 flex-col p-2 overflow-x-auto">
          <div className="bg-white grid grid-cols-1">
            <div className="bg-white grid grid-cols-1">
              <h1 className="text-2xl font-bold text-center mt-3">
                Process <br /> 工程マスタ
              </h1>
              <hr className="my-6 h-0.5 bg-gray-500 opacity-100 dark:opacity-50 border-y-[1px] border-gray-300" />

              <div className="ml-5 text-lg flex justify-between">
                <input
                  className="border-2 border-gray-500 rounded-md w-52 h-9"
                  type="text"
                  placeholder=" Search..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
                <button
                  onClick={exportToCsv}
                  className="bg-blue-500 text-white px-4 py-2 rounded-md mr-5"
                >
                  Export to CSV
                </button>
              </div>

              <div className="flex justify-center items-center mt-5">
                <div className="w-full text-center px-5">
                  <DataTable
                    columns={columns}
                    data={filteredData}
                    pagination
                    paginationPerPage={10}
                    paginationRowsPerPageOptions={[10, 15, 20, 25, 30]}
                    customStyles={{
                      rows: {
                        style: {
                          "&:nth-of-type(odd)": { backgroundColor: "#ffffff" },
                          "&:nth-of-type(even)": { backgroundColor: "#F5F5F5" },
                          minHeight: "50px",
                          textAlign: "center",
                          justifyContent: "center",
                          borderBottom: "1px solid #ccc",
                          borderRight: "1px solid #ccc",
                        },
                      },
                      headCells: {
                        style: {
                          backgroundColor: "#DCDCDC",
                          fontSize: "14px",
                          textAlign: "center",
                          justifyContent: "center",
                          border: "1px solid #ccc",
                        },
                      },
                      cells: {
                        style: {
                          textAlign: "center",
                          justifyContent: "center",
                          border: "1px solid #ccc",
                        },
                      },
                      table: {
                        style: {
                          borderCollapse: "collapse",
                        },
                      },
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white p-3 mt-5">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-6 lg:grid-cols-12 gap-4">
            {/* First button group */}
            <button
              id="F1"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              disabled
            >
              (F1)
            </button>
            <button
              id="F2"
              onClick={() => {
                setIsF2Pressed(true); // เปลี่ยนสถานะเมื่อกด F2
              }}
              disabled={isF2Pressed}
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
            >
              Edit <br />
              編集 (F2)
            </button>
            <button
              id="F3"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={openModal}
              disabled={isF2Pressed}
            >
              New Add <br />
              新規追加 (F3)
            </button>

            {/* Modal */}
            {isModalOpen && (
              <div className="fixed inset-0 flex justify-center items-center bg-gray-500 bg-opacity-50">
                <div
                  className="bg-white p-6 rounded-lg w-1/2 max-h-[80vh] overflow-y-auto"
                  onClick={(e) => e.stopPropagation()}
                >
                  <h2 className="text-xl mb-4">Add New Process</h2>
                  <form onSubmit={handleCreateProcess}>
                    {/* Process_CD */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Process_CD
                      </label>
                      <input
                        type="text"
                        name="Process_CD"
                        value={formData.Process_CD}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Change_CD */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Change_CD
                      </label>
                      <input
                        type="text"
                        name="Change_CD"
                        value={formData.Change_CD}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* ProcessG_CD */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        ProcessG_CD
                      </label>
                      <input
                        type="text"
                        name="ProcessG_CD"
                        value={formData.ProcessG_CD}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* ResourceG_CD */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        ResourceG_CD
                      </label>
                      <input
                        type="text"
                        name="ResourceG_CD"
                        value={formData.ResourceG_CD}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* ManageG_CD */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        ManageG_CD
                      </label>
                      <input
                        type="text"
                        name="ManageG_CD"
                        value={formData.ManageG_CD}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Manhour_Calc */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Manhour_Calc
                      </label>
                      <input
                        type="text"
                        name="Manhour_Calc"
                        value={formData.Manhour_Calc}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Days_Calc */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Days_Calc
                      </label>
                      <input
                        type="text"
                        name="Days_Calc"
                        value={formData.Days_Calc}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Process_Name */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Process_Name
                      </label>
                      <input
                        type="text"
                        name="Process_Name"
                        value={formData.Process_Name}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Process_Abb */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Process_Abb
                      </label>
                      <input
                        type="text"
                        name="Process_Abb"
                        value={formData.Process_Abb}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Process_Symbol */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Process_Symbol
                      </label>
                      <input
                        type="text"
                        name="Process_Symbol"
                        value={formData.Process_Symbol}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Process_Mark */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Process_Mark
                      </label>
                      <input
                        type="text"
                        name="Process_Mark"
                        value={formData.Process_Mark}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Use */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Use
                      </label>
                      <input
                        type="checkbox"
                        name="Use"
                        checked={formData.Use}
                        onChange={handleInputChange}
                        className="p-2 border rounded-md"
                      />
                    </div>

                    {/* For_Plan */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        For_Plan
                      </label>
                      <input
                        type="checkbox"
                        name="For_Plan"
                        checked={formData.For_Plan}
                        onChange={handleInputChange}
                        className="p-2 border rounded-md"
                      />
                    </div>

                    {/* For_Info */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        For_Info
                      </label>
                      <input
                        type="checkbox"
                        name="For_Info"
                        checked={formData.For_Info}
                        onChange={handleInputChange}
                        className="p-2 border rounded-md"
                      />
                    </div>

                    {/* Graph */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Graph
                      </label>
                      <input
                        type="checkbox"
                        name="Graph"
                        checked={formData.Graph}
                        onChange={handleInputChange}
                        className="p-2 border rounded-md"
                      />
                    </div>

                    {/* List */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        List
                      </label>
                      <input
                        type="checkbox"
                        name="List"
                        checked={formData.List}
                        onChange={handleInputChange}
                        className="p-2 border rounded-md"
                      />
                    </div>

                    {/* Outside_On */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Outside_On
                      </label>
                      <input
                        type="checkbox"
                        name="Outside_On"
                        checked={formData.Outside_On}
                        onChange={handleInputChange}
                        className="p-2 border rounded-md"
                      />
                    </div>

                    {/* Outside_Off */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Outside_Off
                      </label>
                      <input
                        type="checkbox"
                        name="Outside_Off"
                        checked={formData.Outside_Off}
                        onChange={handleInputChange}
                        className="p-2 border rounded-md"
                      />
                    </div>

                    {/* End */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        End
                      </label>
                      <input
                        type="checkbox"
                        name="End"
                        checked={formData.End}
                        onChange={handleInputChange}
                        className="p-2 border rounded-md"
                      />
                    </div>

                    {/* Coefficient */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Coefficient
                      </label>
                      <input
                        type="number"
                        name="Coefficient"
                        value={formData.Coefficient}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* M_Coefficient */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        M_Coefficient
                      </label>
                      <input
                        type="number"
                        name="M_Coefficient"
                        value={formData.M_Coefficient}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* P_Coefficient */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        P_Coefficient
                      </label>
                      <input
                        type="number"
                        name="P_Coefficient"
                        value={formData.P_Coefficient}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Before */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Before
                      </label>
                      <input
                        type="number"
                        name="Before"
                        value={formData.Before}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* After */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        After
                      </label>
                      <input
                        type="number"
                        name="After"
                        value={formData.After}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Operation_Time */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Operation_Time
                      </label>
                      <input
                        type="number"
                        name="Operation_Time"
                        value={formData.Operation_Time}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Std_M_CAT */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Std_M_CAT
                      </label>
                      <input
                        type="number"
                        name="Std_M_CAT"
                        value={formData.Std_M_CAT}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Std_M_Time */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Std_M_Time
                      </label>
                      <input
                        type="number"
                        name="Std_M_Time"
                        value={formData.Std_M_Time}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Std_P_CAT */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Std_P_CAT
                      </label>
                      <input
                        type="number"
                        name="Std_P_CAT"
                        value={formData.Std_P_CAT}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Std_P_Time */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Std_P_Time
                      </label>
                      <input
                        type="number"
                        name="Std_P_Time"
                        value={formData.Std_P_Time}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* T_Type */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        T_Type
                      </label>
                      <input
                        type="text"
                        name="T_Type"
                        value={formData.T_Type}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* P_Type */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        P_Type
                      </label>
                      <input
                        type="text"
                        name="P_Type"
                        value={formData.P_Type}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* S_Type */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        S_Type
                      </label>
                      <input
                        type="text"
                        name="S_Type"
                        value={formData.S_Type}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Process_Remark */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Process_Remark
                      </label>
                      <textarea
                        name="Process_Remark"
                        value={formData.Process_Remark}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md h-24 overflow-y-auto"
                      />
                    </div>

                    {/* Save and Close Buttons */}
                    <div className="mt-4 flex justify-end gap-4">
                      <button
                        type="submit"
                        className="bg-blue-500 p-3 rounded-lg text-white"
                      >
                        Save
                      </button>
                      <button
                        type="button"
                        onClick={closeModal}
                        className="bg-gray-500 p-3 rounded-lg text-white"
                      >
                        Close
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            )}

            <button
              id="F4"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={handleSelectClick}
            >
              Select <br />
              選択 (F4)
            </button>

            <button
              id="F5"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              disabled
            >
              (F5)
            </button>
            <button
              id="F6"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              disabled
            >
              (F6)
            </button>
            <button
              id="F7"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              disabled
            >
              (F7)
            </button>
            <button
              id="F8"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              disabled
            >
              (F8)
            </button>
            <button
              id="F9"
              onClick={() => {
                data.forEach((row) => {
                  Object.keys(row).forEach((field) => {
                    if (editedData[row.Process_CD]?.[field] !== undefined) {
                      handleUpdateProcess(row.Process_CD, field);
                    }
                  });
                });
              }}
              disabled={!isF2Pressed}
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
            >
              Save <br />
              保存 (F9)
            </button>
            <button
              id="F10"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={handleDeleteProcess}
              disabled={isButtonDisabled} // ตั้งค่าปุ่มให้ disabled ตามสถานะ
            >
              Delete <br />
              消去 (F10)
            </button>
            <button
              id="F11"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={() => {
                window.location.reload(); // รีเฟรชหน้า
              }}
              disabled={false} // ตั้งค่า disabled เป็น false ถ้าคุณต้องการให้ปุ่มทำงาน
            >
              Clear <br />
              クリア (F11)
            </button>

            <button
              id="F12"
              onClick={handleF12Click}
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
            >
              Back <br />
              戻る (F12)
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
