import React from 'react';
import { useAuth } from '../hooks/use-auth';

const WorkerMenuDebug = () => {
  const { authUser } = useAuth();

  if (!authUser) {
    return <div className="p-4 bg-red-100 text-red-800 rounded">No user logged in</div>;
  }

  return (
    <div className="p-4 bg-blue-100 text-blue-800 rounded mb-4">
      <h3 className="font-bold">Debug Info:</h3>
      <p>User Name: {authUser.User_Name || 'N/A'}</p>
      <p>Worker CD: {authUser.Worker_CD || 'N/A'}</p>
      <p>Worker Menu: {authUser.Worker_Menu || 'N/A'}</p>
      <p>Worker Menu Type: {typeof authUser.Worker_Menu}</p>
      <p>Is Worker Menu "5": {authUser.Worker_Menu && authUser.Worker_Menu.toString() === "5" ? "Yes" : "No"}</p>
      <p>Is Worker Menu "6": {authUser.Worker_Menu && authUser.Worker_Menu.toString() === "6" ? "Yes" : "No"}</p>
    </div>
  );
};

export default WorkerMenuDebug;
