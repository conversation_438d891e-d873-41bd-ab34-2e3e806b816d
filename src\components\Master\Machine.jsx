import React, { useState, useEffect } from "react";
import Navbar from "../Navbar";
import Sidebar from "../Sidebar";
import DataTable from "react-data-table-component";
import axios from "axios";
import Papa from "papaparse";
import { useNavigate } from "react-router-dom";
import Swal from "sweetalert2";

export function Machine() {
  const apiUrl_4000 = import.meta.env.VITE_BACKEND_URL;

  const [isF2Pressed, setIsF2Pressed] = useState(false);
  const [isModified, setIsModified] = useState(false);
  const navigate = useNavigate();
  const [showCheckbox, setShowCheckbox] = useState(false);
  const [isButtonDisabled, setIsButtonDisabled] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [data, setData] = useState([]);
  const [selectedResources, setSelectedResources] = useState([]);
  const [editedData, setEditedData] = useState({});
  const [selectedRowForCopy, setSelectedRowForCopy] = useState(null); // For radio selection

  const fetchResource = async () => {
    try {
      const response = await axios.get(
        `${apiUrl_4000}/resource/fetch-resource`
      );
      // console.log("Fetched data:", response.data);
      setData(response.data.message || []);
    } catch (error) {
      // console.error("Error fetching resource:", error);
    }
  };

  useEffect(() => {
    fetchResource();
  }, []);

  const [formData, setFormData] = useState({
    Resource_CD: "",
    Change_CD: "",
    ResourceG_CD: "",
    CostG_CD: "",
    ManageG_CD: "",
    Resource_Name: "",
    Resource_Abb: "",
    Resource_Symbol: "",
    Resource_Mark: "",
    Use: false,
    End: false,
    M_Coefficient: "",
    P_Coefficient: "",
    Before: "",
    After: "",
    T_Type: "",
    P_Type: "",
    Resource_Remark: "",
  });

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleChange = (e, resourceCd, field) => {
    setEditedData({
      ...editedData,
      [resourceCd]: {
        ...editedData[resourceCd],
        [field]: e.target.value,
      },
    });
  };

  const openModal = () => {
    if (selectedRowForCopy) {
      // Copy all fields except Resource_CD
      const { Resource_CD, ...rest } = selectedRowForCopy;
      setFormData({
        Resource_CD: "",
        ...rest,
      });
    } else {
      setFormData({
        Resource_CD: "",
        Change_CD: "",
        ResourceG_CD: "",
        CostG_CD: "",
        ManageG_CD: "",
        Resource_Name: "",
        Resource_Abb: "",
        Resource_Symbol: "",
        Resource_Mark: "",
        Use: false,
        End: false,
        M_Coefficient: "",
        P_Coefficient: "",
        Before: "",
        After: "",
        T_Type: "",
        P_Type: "",
        Resource_Remark: "",
      });
    }
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  const handleSelectClick = () => {
    document.getElementById("F1").disabled = true;
    document.getElementById("F2").disabled = true;
    document.getElementById("F3").disabled = true;
    document.getElementById("F4").disabled = true;
    document.getElementById("F5").disabled = true;
    document.getElementById("F6").disabled = true;
    document.getElementById("F7").disabled = true;
    document.getElementById("F8").disabled = true;
    document.getElementById("F9").disabled = true;
    document.getElementById("F10").disabled = false;
    document.getElementById("F11").disabled = false;
    document.getElementById("F12").disabled = false;

    setIsButtonDisabled(false);
    handleCheckboxToggle();
  };

  const handleCheckboxChange = (event, resourceCd) => {
    const isChecked = event.target.checked;

    setSelectedResources((prev) => {
      if (isChecked) {
        return [...prev, resourceCd]; // เพิ่ม Resource_CD ที่เลือก
      } else {
        return prev.filter((cd) => cd !== resourceCd); // เอา Resource_CD ออกจากรายการ
      }
    });
  };

  const handleCheckboxChangeForEdit = (e, resourceCd, field) => {
    const { checked } = e.target;

    // อัปเดตข้อมูลใน editedData หรือ state ของ row
    setEditedData((prevData) => ({
      ...prevData,
      [resourceCd]: {
        ...prevData[resourceCd],
        [field]: checked,
      },
    }));
  };

  const handleCreateResource = async (e) => {
    e.preventDefault();

    if (
      formData.T_Type.length > 1 ||
      formData.P_Type.length > 1 ||
      (!/^[A-Za-z]*$/.test(formData.T_Type) && formData.T_Type !== "") ||
      (!/^[A-Za-z]*$/.test(formData.P_Type) && formData.P_Type !== "")
    ) {
      Swal.fire({
        title: "Validation Error",
        text: "T_Type and P_Type must be a single character (A-Z, a-z) or (0-9).",
        icon: "error",
        confirmButtonText: "OK",
      });
      return;
    }

    try {
      const token = localStorage.getItem("authToken");

      const response = await axios.post(
        `${apiUrl_4000}/resource/create-resource`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (response.status === 200) {
        Swal.fire({
          title: "Success",
          text: "Resource created successfully.",
          icon: "success",
          confirmButtonText: "OK",
        });

        // อัปเดตข้อมูลในตารางหลังจากสร้างสำเร็จ
        fetchResource();

        // ล้างค่าฟอร์ม
        setFormData({
          Resource_CD: "",
          Change_CD: "",
          ResourceG_CD: "",
          CostG_CD: "",
          ManageG_CD: "",
          Resource_Name: "",
          Resource_Abb: "",
          Resource_Symbol: "",
          Resource_Mark: "",
          Use: false,
          End: false,
          M_Coefficient: "",
          P_Coefficient: "",
          Before: "",
          After: "",
          T_Type: "",
          P_Type: "",
          Resource_Remark: "",
        });

        // Clear the selected radio button by setting selectedRowForCopy to null
        setSelectedRowForCopy(null);

        // ปิด Modal
        closeModal();
      }
    } catch (error) {
      console.error("Error creating resource:", error);
      Swal.fire({
        title: "Error",
        text: error.response?.data?.message || "Failed to create resource.",
        icon: "error",
        confirmButtonText: "OK",
      });
    }
  };

  const handleUpdateResource = async (resourceCd) => {
    const payload = { Resource_CD: resourceCd };
    let isEdited = false;

    const editedFields = Object.keys(editedData[resourceCd] || {});

    for (const field of editedFields) {
      const newValue = editedData[resourceCd]?.[field];
      const oldValue = data.find((row) => row.Resource_CD === resourceCd)?.[
        field
      ];

      if (newValue !== oldValue) {
        if (
          (field === "T_Type" || field === "P_Type") &&
          (newValue.length > 1 ||
            (!/^[A-Za-z]*$/.test(newValue) && newValue !== ""))
        ) {
          Swal.fire({
            title: "Validation Error",
            text: `${field} must be a single character (A-Z, a-z) or (0-9).`,
            icon: "error",
            confirmButtonText: "OK",
          });
          return;
        }

        payload[field] = newValue === "" ? null : newValue;
        isEdited = true;
      }
    }

    if (!isEdited) {
      Swal.fire({
        icon: "info",
        title: "No changes made!",
        text: "Please make sure to edit something before saving.",
      });
      return;
    }

    let isSaving = false;

    if (isSaving) {
      return;
    }

    try {
      isSaving = true;

      const response = await axios.put(
        `${apiUrl_4000}/resource/update-resource`,
        payload
      );

      const updatedData = [...data];
      const rowIndex = updatedData.findIndex(
        (row) => row.Resource_CD === resourceCd
      );
      if (rowIndex !== -1) {
        Object.keys(payload).forEach((field) => {
          if (field !== "Resource_CD") {
            updatedData[rowIndex][field] = payload[field];
          }
        });
        setData(updatedData);
      }

      Swal.fire({
        icon: "success",
        title: "Edit Successfully!",
        text: "Resource data has been updated.",
      });
    } catch (error) {
      Swal.fire({
        icon: "error",
        title: "Something went wrong!",
        text: "Please try again later.",
      });
      console.error(error);
    } finally {
      isSaving = false;
    }
  };

  const handleDeleteResource = async () => {
    if (selectedResources.length === 0) {
      Swal.fire({
        title: "No Selection",
        text: "Please select at least one item to delete.",
        icon: "warning",
        confirmButtonText: "OK",
      });
      return;
    }

    const resourceList = selectedResources.join(", "); // รวม Resource_CD ที่เลือกเป็น String

    try {
      const confirmResult = await Swal.fire({
        title: "Confirm Delete",
        html: `Are you sure you want to delete the following items?<br>Resource CDs: <b>${resourceList}</b>`,
        icon: "warning",
        showCancelButton: true,
        confirmButtonText: "Yes",
        cancelButtonText: "No",
      });

      if (confirmResult.isConfirmed) {
        const token = localStorage.getItem("authToken");

        const response = await axios.delete(
          `${apiUrl_4000}/resource/delete-resource`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
            data: selectedResources.map((cd) => ({ Resource_CD: cd })), // ส่งเป็นอาเรย์ของ { Resource_CD }
          }
        );

        if (response.status === 200) {
          Swal.fire({
            title: "Deleted!",
            text: "The selected resources have been deleted.",
            icon: "success",
            confirmButtonText: "OK",
          });

          // รีเฟรชข้อมูล
          fetchResource();

          setSelectedResources([]);
        } else {
          Swal.fire({
            title: "Error",
            text: response.data.message || "An error occurred while deleting.",
            icon: "error",
            confirmButtonText: "OK",
          });
        }
      }
    } catch (error) {
      console.error("Error in handleDeleteResource:", error);
      Swal.fire({
        title: "Error",
        text: "Something went wrong while deleting.",
        icon: "error",
        confirmButtonText: "OK",
      });
    }
  };

  useEffect(() => {
    const initialEditedData = data.reduce((acc, row, index) => {
      if (!editedData[index]) {
        acc[index] = { ...row };
      }
      return acc;
    }, {});

    if (Object.keys(initialEditedData).length > 0) {
      setEditedData(initialEditedData);
    }
  }, [data]);

  const filteredData = data.filter((row) => {
    return Object.values(row).some((value) =>
      String(value).toLowerCase().includes(searchTerm.toLowerCase())
    );
  });

  const handleF12Click = async () => {
    try {
      const confirmResult = await Swal.fire({
        title: "Confirm",
        html: isModified
          ? "The data has been edited. Do you want to go back to master 3?<br>ข้อมูลถูกแก้ไขต้องการกลับไปที่หน้า master 3 หรือไม่?<br>データは編集されました。master 3 に戻りますか？"
          : "Do you want to go back to master 3?<br>คุณต้องการกลับไปที่หน้า master 3 หรือไม่?<br>master 3 に戻りますか？",
        icon: "warning",
        showCancelButton: true,
        confirmButtonText: "Yes",
        cancelButtonText: "No",
      });

      if (confirmResult.isConfirmed) {
        setIsModified(false);
        navigate("/master3");
      }
    } catch (error) {
      console.error("Error in handleF12Click:", error);
      Swal.fire({
        title: "Error",
        text: "Try again.",
        icon: "error",
        confirmButtonText: "OK",
      });
    }
  };

  // ฟังก์ชันสำหรับ Export ข้อมูลเป็น CSV
  const exportToCsv = () => {
    const csvData = data.map((row) => ({
      Resource_CD: row.Resource_CD,
      Change_CD: row.Change_CD,
      ResourceG_CD: row.ResourceG_CD,
      CostG_CD: row.CostG_CD,
      ManageG_CD: row.ManageG_CD,
      Resource_Name: row.Resource_Name,
      Resource_Abb: row.Resource_Abb,
      Resource_Symbol: row.Resource_Symbol,
      Resource_Mark: row.Resource_Mark,
      Use: row.Use,
      End: row.End,
      M_Coefficient: row.M_Coefficient,
      P_Coefficient: row.P_Coefficient,
      Before: row.Before,
      After: row.After,
      T_Type: row.T_Type,
      P_Type: row.P_Type,
      Resource_Remark: row.Resource_Remark,
    }));

    const csv = Papa.unparse(csvData); // แปลง JSON เป็น CSV
    const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" });

    // ดาวน์โหลดไฟล์ CSV
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", "Machine_data.csv");
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleCheckboxToggle = () => {
    setShowCheckbox(!showCheckbox);
  };

  const columns = [
    {
      name: "SelectForCopy",
      selector: (row) => (
        <input
          type="radio"
          name="row-radio"
          checked={
            selectedRowForCopy &&
            selectedRowForCopy.Resource_CD === row.Resource_CD
          }
          onChange={() => setSelectedRowForCopy(row)}
        />
      ),
      width: "150px",
      omit: false,
    },
    {
      name: "SelectForDelete",
      selector: (row) =>
        showCheckbox ? (
          <input
            type="checkbox"
            className="checkbox"
            checked={selectedResources.includes(row.Resource_CD)}
            onChange={(e) => handleCheckboxChange(e, row.Resource_CD)}
          />
        ) : null, // ซ่อน checkbox หาก showCheckbox เป็น false
      width: "150px",
      omit: !showCheckbox,
    },
    {
      name: "Resource_CD",
      selector: (row) => (
        <input
          className="w-full p-2 bg-transparent"
          type="text"
          value={
            editedData[row.Resource_CD]?.Resource_CD !== undefined
              ? editedData[row.Resource_CD]?.Resource_CD
              : row.Resource_CD || ""
          }
          onChange={(e) => handleChange(e, row.Resource_CD, "Resource_CD")}
          disabled
        />
      ),
      width: "190px",
      sortable: true,
      sortFunction: (rowA, rowB) => {
        const valA =
          editedData[rowA.Resource_CD]?.Resource_CD || rowA.Resource_CD || "";
        const valB =
          editedData[rowB.Resource_CD]?.Resource_CD || rowB.Resource_CD || "";

        return valA.localeCompare(valB);
      },
    },
    {
      name: "Change_CD",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Resource_CD]?.Change_CD !== undefined
              ? editedData[row.Resource_CD]?.Change_CD
              : row.Change_CD || ""
          }
          onChange={(e) => handleChange(e, row.Resource_CD, "Change_CD")}
          disabled={!isF2Pressed}
        />
      ),
      width: "190px",
    },
    {
      name: "ResourceG_CD",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Resource_CD]?.ResourceG_CD !== undefined
              ? editedData[row.Resource_CD]?.ResourceG_CD
              : row.ResourceG_CD || ""
          }
          onChange={(e) => handleChange(e, row.Resource_CD, "ResourceG_CD")}
          disabled={!isF2Pressed}
        />
      ),
      width: "190px",
    },
    {
      name: "CostG_CD",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Resource_CD]?.CostG_CD !== undefined
              ? editedData[row.Resource_CD]?.CostG_CD
              : row.CostG_CD || ""
          }
          onChange={(e) => handleChange(e, row.Resource_CD, "CostG_CD")}
          disabled={!isF2Pressed}
        />
      ),
      width: "190px",
    },
    {
      name: "ManageG_CD",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Resource_CD]?.ManageG_CD !== undefined
              ? editedData[row.Resource_CD]?.ManageG_CD
              : row.ManageG_CD || ""
          }
          onChange={(e) => handleChange(e, row.Resource_CD, "ManageG_CD")}
          disabled={!isF2Pressed}
        />
      ),
      width: "190px",
    },
    {
      name: "Resource_Name",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Resource_CD]?.Resource_Name !== undefined
              ? editedData[row.Resource_CD]?.Resource_Name
              : row.Resource_Name || ""
          }
          onChange={(e) => handleChange(e, row.Resource_CD, "Resource_Name")}
          disabled={!isF2Pressed}
        />
      ),
      width: "190px",
    },
    {
      name: "Resource_Abb",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Resource_CD]?.Resource_Abb !== undefined
              ? editedData[row.Resource_CD]?.Resource_Abb
              : row.Resource_Abb || ""
          }
          onChange={(e) => handleChange(e, row.Resource_CD, "Resource_Abb")}
          disabled={!isF2Pressed}
        />
      ),
      width: "190px",
    },
    {
      name: "Resource_Symbol",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Resource_CD]?.Resource_Symbol !== undefined
              ? editedData[row.Resource_CD]?.Resource_Symbol
              : row.Resource_Symbol || ""
          }
          onChange={(e) => handleChange(e, row.Resource_CD, "Resource_Symbol")}
          disabled={!isF2Pressed}
        />
      ),
      width: "190px",
    },
    {
      name: "Resource_Mark",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Resource_CD]?.Resource_Mark !== undefined
              ? editedData[row.Resource_CD]?.Resource_Mark
              : row.Resource_Mark || ""
          }
          onChange={(e) => handleChange(e, row.Resource_CD, "Resource_Mark")}
          disabled={!isF2Pressed}
        />
      ),
      width: "190px",
    },
    {
      name: "Use",
      selector: (row) => (
        <input
          type="checkbox"
          checked={editedData[row.Resource_CD]?.Use ?? row.Use}
          className="mx-auto"
          onChange={(e) =>
            handleCheckboxChangeForEdit(e, row.Resource_CD, "Use")
          }
          disabled={!isF2Pressed}
        />
      ),
      width: "120px",
    },
    {
      name: "End",
      selector: (row) => (
        <input
          type="checkbox"
          checked={editedData[row.Resource_CD]?.End ?? row.End}
          className="mx-auto"
          onChange={(e) =>
            handleCheckboxChangeForEdit(e, row.Resource_CD, "End")
          }
          disabled={!isF2Pressed}
        />
      ),
      width: "120px",
    },

    {
      name: "M_Coefficient",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="number"
          value={
            editedData[row.Resource_CD]?.M_Coefficient !== undefined
              ? editedData[row.Resource_CD]?.M_Coefficient
              : row.M_Coefficient || ""
          }
          onChange={(e) => handleChange(e, row.Resource_CD, "M_Coefficient")}
          disabled={!isF2Pressed}
        />
      ),
      width: "190px",
    },
    {
      name: "P_Coefficient",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="number"
          value={
            editedData[row.Resource_CD]?.P_Coefficient !== undefined
              ? editedData[row.Resource_CD]?.P_Coefficient
              : row.P_Coefficient ?? ""
          }
          onChange={(e) => handleChange(e, row.Resource_CD, "P_Coefficient")}
          disabled={!isF2Pressed}
        />
      ),
      width: "170px",
    },
    {
      name: "Before",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="number"
          value={
            editedData[row.Resource_CD]?.Before !== undefined
              ? editedData[row.Resource_CD]?.Before
              : row.Before ?? ""
          }
          onChange={(e) => handleChange(e, row.Resource_CD, "Before")}
          disabled={!isF2Pressed}
        />
      ),
      width: "170px",
    },
    {
      name: "After",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="number"
          value={
            editedData[row.Resource_CD]?.After !== undefined
              ? editedData[row.Resource_CD]?.After
              : row.After ?? ""
          }
          onChange={(e) => handleChange(e, row.Resource_CD, "After")}
          disabled={!isF2Pressed}
        />
      ),
      width: "170px",
    },
    {
      name: "T_Type",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Resource_CD]?.T_Type !== undefined
              ? editedData[row.Resource_CD]?.T_Type
              : row.T_Type || ""
          }
          onChange={(e) => handleChange(e, row.Resource_CD, "T_Type")}
          disabled={!isF2Pressed}
        />
      ),
      width: "190px",
    },
    {
      name: "P_Type",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Resource_CD]?.P_Type !== undefined
              ? editedData[row.Resource_CD]?.P_Type
              : row.P_Type || ""
          }
          onChange={(e) => handleChange(e, row.Resource_CD, "P_Type")}
          disabled={!isF2Pressed}
        />
      ),
      width: "190px",
    },
    {
      name: "Resource_Remark",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          style={{
            width: "fit-content",
            minWidth: "340px",
            maxWidth: "100%",
          }}
          value={
            editedData[row.Resource_CD]?.Resource_Remark !== undefined
              ? editedData[row.Resource_CD]?.Resource_Remark
              : row.Resource_Remark || ""
          }
          onChange={(e) => handleChange(e, row.Resource_CD, "Resource_Remark")}
          disabled={!isF2Pressed}
        />
      ),
      width: "300px",
    },
  ];

  return (
    <div className="flex bg-[#E9EFEC] h-[100vh]">
      <Sidebar />
      <div className="flex flex-col w-full mr-2 ml-2">
        <Navbar />
        <div className="flex-1 flex-col p-2 overflow-x-auto">
          <div className="bg-white grid grid-cols-1">
            <div className="bg-white grid grid-cols-1">
              <h1 className="text-2xl font-bold text-center mt-3">
                Machine <br /> 機械マスタ
              </h1>
              <hr className="my-6 h-0.5 bg-gray-500 opacity-100 dark:opacity-50 border-y-[1px] border-gray-300" />

              <div className="ml-5 text-lg flex justify-between">
                <input
                  className="border-2 border-gray-500 rounded-md w-52 h-9"
                  type="text"
                  placeholder=" Search..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
                <button
                  onClick={exportToCsv}
                  className="bg-blue-500 text-white px-4 py-2 rounded-md mr-5"
                >
                  Export to CSV
                </button>
              </div>

              <div className="flex justify-center items-center mt-5">
                <div className="w-full text-center px-5">
                  <DataTable
                    columns={columns}
                    data={filteredData}
                    pagination
                    paginationPerPage={10}
                    paginationRowsPerPageOptions={[10, 15, 20, 25, 30]}
                    customStyles={{
                      rows: {
                        style: {
                          "&:nth-of-type(odd)": { backgroundColor: "#ffffff" },
                          "&:nth-of-type(even)": { backgroundColor: "#F5F5F5" },
                          minHeight: "50px",
                          textAlign: "center",
                          justifyContent: "center",
                          borderBottom: "1px solid #ccc",
                          borderRight: "1px solid #ccc",
                        },
                      },
                      headCells: {
                        style: {
                          backgroundColor: "#DCDCDC",
                          fontSize: "14px",
                          textAlign: "center",
                          justifyContent: "center",
                          border: "1px solid #ccc",
                        },
                      },
                      cells: {
                        style: {
                          textAlign: "center",
                          justifyContent: "center",
                          border: "1px solid #ccc",
                        },
                      },
                      table: {
                        style: {
                          borderCollapse: "collapse",
                        },
                      },
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="bg-white p-3 mt-5">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-6 lg:grid-cols-12 gap-4">
            {/* First button group */}
            <button
              id="F1"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              disabled
            >
              (F1)
            </button>
            <button
              id="F2"
              onClick={() => {
                setIsF2Pressed(true); // เปลี่ยนสถานะเมื่อกด F2
              }}
              disabled={isF2Pressed}
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
            >
              Edit <br />
              編集 (F2)
            </button>
            <button
              id="F3"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={openModal}
              disabled={isF2Pressed}
            >
              New Add <br />
              新規追加 (F3)
            </button>

            {/* Modal */}
            {isModalOpen && (
              <div className="fixed inset-0 flex justify-center items-center bg-gray-500 bg-opacity-50">
                <div
                  className="bg-white p-6 rounded-lg w-1/2 max-h-[80vh] overflow-y-auto"
                  onClick={(e) => e.stopPropagation()}
                >
                  <h2 className="text-xl mb-4">Add New Resource</h2>
                  <form onSubmit={handleCreateResource}>
                    {/* Resource_CD */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Resource_CD
                      </label>
                      <input
                        type="text"
                        name="Resource_CD"
                        value={formData.Resource_CD}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Change_CD */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Change_CD
                      </label>
                      <input
                        name="Change_CD"
                        value={formData.Change_CD}
                        onChange={handleInputChange}
                        type="text"
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* ResourceG_CD */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        ResourceG_CD
                      </label>
                      <input
                        name="ResourceG_CD"
                        value={formData.ResourceG_CD}
                        onChange={handleInputChange}
                        type="text"
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* CostG_CD */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        CostG_CD
                      </label>
                      <input
                        name="CostG_CD"
                        value={formData.CostG_CD}
                        onChange={handleInputChange}
                        type="text"
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* ManageG_CD */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        ManageG_CD
                      </label>
                      <input
                        name="ManageG_CD"
                        value={formData.ManageG_CD}
                        onChange={handleInputChange}
                        type="text"
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Resource_Name */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Resource_Name
                      </label>
                      <input
                        name="Resource_Name"
                        value={formData.Resource_Name}
                        onChange={handleInputChange}
                        type="text"
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Resource_Abb */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Resource_Abb
                      </label>
                      <input
                        name="Resource_Abb"
                        value={formData.Resource_Abb}
                        onChange={handleInputChange}
                        type="text"
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Resource_Symbol */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Resource_Symbol
                      </label>
                      <input
                        name="Resource_Symbol"
                        value={formData.Resource_Symbol}
                        onChange={handleInputChange}
                        type="text"
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Resource_Mark */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Resource_Mark
                      </label>
                      <input
                        name="Resource_Mark"
                        value={formData.Resource_Mark}
                        onChange={handleInputChange}
                        type="text"
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Use */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Use
                      </label>
                      <input
                        name="Use"
                        checked={formData.Use}
                        onChange={handleInputChange}
                        type="checkbox"
                        className="p-2 border rounded-md"
                      />
                    </div>

                    {/* End */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        End
                      </label>
                      <input
                        name="End"
                        checked={formData.End}
                        onChange={handleInputChange}
                        type="checkbox"
                        className="p-2 border rounded-md"
                      />
                    </div>

                    {/* M_Coefficient */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        M_Coefficient
                      </label>
                      <input
                        name="M_Coefficient"
                        value={formData.M_Coefficient}
                        onChange={handleInputChange}
                        type="number"
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* P_Coefficient */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        P_Coefficient
                      </label>
                      <input
                        name="P_Coefficient"
                        value={formData.P_Coefficient}
                        onChange={handleInputChange}
                        type="number"
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Before */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Before
                      </label>
                      <input
                        name="Before"
                        value={formData.Before}
                        onChange={handleInputChange}
                        type="number"
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* After */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        After
                      </label>
                      <input
                        name="After"
                        value={formData.After}
                        onChange={handleInputChange}
                        type="number"
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* T_Type */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        T_Type
                      </label>
                      <input
                        name="T_Type"
                        value={formData.T_Type}
                        onChange={handleInputChange}
                        type="text"
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* P_Type */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        P_Type
                      </label>
                      <input
                        name="P_Type"
                        value={formData.P_Type}
                        onChange={handleInputChange}
                        type="text"
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Resource_Remark */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Resource_Remark
                      </label>
                      <textarea
                        name="Resource_Remark"
                        value={formData.Resource_Remark}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md h-24 overflow-y-auto"
                      />
                    </div>

                    {/* Save and Close Buttons */}
                    <div className="mt-4 flex justify-end gap-4">
                      <button
                        type="submit"
                        className="bg-blue-500 p-3 rounded-lg text-white"
                      >
                        Save
                      </button>
                      <button
                        type="button"
                        onClick={closeModal}
                        className="bg-gray-500 p-3 rounded-lg text-white"
                      >
                        Close
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            )}

            <button
              id="F4"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={handleSelectClick}
            >
              Select <br />
              選択 (F4)
            </button>

            <button
              id="F5"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              disabled
            >
              (F5)
            </button>
            <button
              id="F6"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              disabled
            >
              (F6)
            </button>
            <button
              id="F7"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              disabled
            >
              (F7)
            </button>
            <button
              id="F8"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              disabled
            >
              (F8)
            </button>
            <button
              id="F9"
              onClick={() => {
                data.forEach((row) => {
                  Object.keys(row).forEach((field) => {
                    if (editedData[row.Resource_CD]?.[field] !== undefined) {
                      handleUpdateResource(row.Resource_CD, field);
                    }
                  });
                });
              }}
              disabled={!isF2Pressed}
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
            >
              Save <br />
              保存 (F9)
            </button>
            <button
              id="F10"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={handleDeleteResource}
              disabled={isButtonDisabled} // ตั้งค่าปุ่มให้ disabled ตามสถานะ
            >
              Delete <br />
              消去 (F10)
            </button>
            <button
              id="F11"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={() => {
                window.location.reload(); // รีเฟรชหน้า
              }}
              disabled={false} // ตั้งค่า disabled เป็น false ถ้าคุณต้องการให้ปุ่มทำงาน
            >
              Clear <br />
              クリア (F11)
            </button>

            <button
              id="F12"
              onClick={handleF12Click}
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
            >
              Back <br />
              戻る (F12)
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
