import { useAuth } from "../hooks/use-auth";
import { Navigate, useLocation } from "react-router-dom";
import { useEffect } from "react";

// Define allowed routes for each Worker_Menu
const ALLOWED_ROUTES = {
  "5": ["/production", "/result-info"],
  "6": ["/qc", "/result-info", "/plan-list"]
};

const ProtectedRoute = ({ children }) => {
  const { authUser, initialLoading } = useAuth();
  const location = useLocation();

  if (initialLoading) return null; // หรือ loading spinner

  if (!authUser) {
    return <Navigate to="/" replace />;
  }

  // Handle Worker_Menu-based redirection
  useEffect(() => {
    if (authUser && authUser.Worker_Menu) {
      const workerMenu = authUser.Worker_Menu.toString();
      const allowedRoutes = ALLOWED_ROUTES[workerMenu];

      if (allowedRoutes && !allowedRoutes.includes(location.pathname)) {
        // Redirect to the first allowed route (home page for that Worker_Menu)
        window.location.replace(allowedRoutes[0]);
        return;
      }
    }
  }, [authUser, location.pathname]);

  // Block access to other routes for Worker_Menu "5" and "6"
  if (authUser && authUser.Worker_Menu) {
    const workerMenu = authUser.Worker_Menu.toString();
    const allowedRoutes = ALLOWED_ROUTES[workerMenu];

    if (allowedRoutes && !allowedRoutes.includes(location.pathname)) {
      return <Navigate to={allowedRoutes[0]} replace />;
    }
  }

  return children;
};

export default ProtectedRoute;
