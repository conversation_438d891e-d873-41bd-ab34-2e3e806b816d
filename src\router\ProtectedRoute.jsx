import { useAuth } from "../hooks/use-auth";
import { Navigate, useLocation } from "react-router-dom";
import { useEffect } from "react";

const ProtectedRoute = ({ children }) => {
  const { authUser, initialLoading } = useAuth();
  const location = useLocation();

  if (initialLoading) return null; // หรือ loading spinner

  if (!authUser) {
    return <Navigate to="/" replace />;
  }

  // Handle Worker_Menu-based redirection
  useEffect(() => {
    if (authUser && authUser.Worker_Menu) {
      const workerMenu = authUser.Worker_Menu.toString();

      // If Worker_Menu is "5", redirect to /production (except if already there)
      if (workerMenu === "5" && location.pathname !== "/production") {
        window.location.replace("/production");
        return;
      }

      // If Worker_Menu is "6", redirect to /qc (except if already there)
      if (workerMenu === "6" && location.pathname !== "/qc") {
        window.location.replace("/qc");
        return;
      }
    }
  }, [authUser, location.pathname]);

  // Block access to other routes for Worker_Menu "5" and "6"
  if (authUser && authUser.Worker_Menu) {
    const workerMenu = authUser.Worker_Menu.toString();

    if (workerMenu === "5" && location.pathname !== "/production") {
      return <Navigate to="/production" replace />;
    }

    if (workerMenu === "6" && location.pathname !== "/qc") {
      return <Navigate to="/qc" replace />;
    }
  }

  return children;
};

export default ProtectedRoute;
