import React, { useState, useEffect } from "react";
import { HiChevronLeft, HiChevronRight } from "react-icons/hi";

export default function QR_CS_List() {
  const apiUrl_5173 = import.meta.env.VITE_FRONTEND_URL;

  const [planlistData, setPlanlistData] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  // ฟังก์ชันในการแปลงวันที่
const formatDateUTC = (date) => {
  if (!date || isNaN(Date.parse(date))) return date;

  const d = new Date(date);
  const day = String(d.getUTCDate()).padStart(2, "0");
  const month = String(d.getUTCMonth() + 1).padStart(2, "0");
  const year = d.getUTCFullYear();
  const hours = String(d.getUTCHours()).padStart(2, "0");
  const minutes = String(d.getUTCMinutes()).padStart(2, "0");
  const seconds = String(d.getUTCSeconds()).padStart(2, "0");

  return `${day}-${month}-${year} ${hours}:${minutes}:${seconds}`;
};

const formatDate = (date) => {
  if (!date || isNaN(Date.parse(date))) return date;

  const d = new Date(date);
  const day = String(d.getUTCDate()).padStart(2, "0");
  const month = String(d.getUTCMonth() + 1).padStart(2, "0");
  const year = String(d.getUTCFullYear()).slice(-2); // ✅ แค่ 2 หลักท้าย

  return `${day}/${month}/${year}`;
};

const formatDates = (date) => {
  if (!date || isNaN(Date.parse(date))) return date;

  const d = new Date(date);
  const day = String(d.getUTCDate()).padStart(2, "0");
  const month = String(d.getUTCMonth() + 1).padStart(2, "0");
  const year = d.getUTCFullYear();

  return `${day}/${month}/${year}`;
};

  useEffect(() => {
    const handleMessage = (event) => {
      // ตรวจสอบ origin
      if (event.origin !== `${apiUrl_5173}`) {
        return;
      }

      const { status, data } = event.data;

     
if (status === 200) {
  if (Array.isArray(data)) {
    setPlanlistData(data); // ใช้ data ตรงๆ เพราะคือ array แล้ว
  } else {
    console.error("Received data is not an array.");
  }
} else {
  console.error("Failed to receive data.");
}
    };

    window.addEventListener("message", handleMessage);

    return () => {
      window.removeEventListener("message", handleMessage);
    };
  }, []);

  // console.log("planlistData", planlistData);

  const totalPages = Math.ceil(planlistData.length / rowsPerPage);
  const startIndex = (currentPage - 1) * rowsPerPage;
  const endIndex = startIndex + rowsPerPage;
  const displayedData = planlistData.slice(startIndex, endIndex);

  const goToNextPage = () => {
    if (currentPage < totalPages) setCurrentPage(currentPage + 1);
  };

  const goToPrevPage = () => {
    if (currentPage > 1) setCurrentPage(currentPage - 1);
  };

const exportToCSV = () => {
  if (planlistData.length === 0) return;

  const headers = [
    "Product_Name", "Order_No", "Customer_Abb", "Parts_No", "Cost_No", "Process_No", "Process_Name", "ProcessG_Name",
    "Process_Date", "Process_Time", "Machine_CD", "Machine_Name", "CostG_CD", "Machine_Time", "M_Coefficient", "Machine_Cost_Time",
    "Person_CD", "Person_Name", "Person_Time", "P_Coefficient", "Person_Cost_Time", "Process_Qty", "Cs_Complete_Date",
    "Reg_Date", "Upd_Date", "Pt_Material", "Pt_Qty", "OdPt_No", "OdPtCs_No", "Pt_Split", "Pt_Spare_Qty", "Pl_Progress_CD",
    "Pl_Schedule_CD", "Pt_Instructions", "Pt_Remark", "Pt_Information", "Pl_Reg_Person_CD", "Pt_Delivery", "Pt_Complete_Date",
    "Pt_Confirm_Date", "Pt_CAT1", "Pt_CAT2", "Pt_CAT3", "Pt_Pending", "Max_No", "Priority_Rate", "Unit_Price", "Price_Process_Qty",
    ...Array.from({ length: 36 }, (_, i) => `PPC${i + 1}`),
    ...Array.from({ length: 36 }, (_, i) => `PPD${i + 1}`),
    ...Array.from({ length: 36 }, (_, i) => `RPD${i + 1}`),
    "Money_Object"
  ];
const formatValue = (val) => {
  if (typeof val === "boolean") return val.toString().toUpperCase(); // TRUE / FALSE
  return val ?? ""; // null/undefined เป็นค่าว่าง
};
  const rows = planlistData.map(plan => [
    plan.Product_Name, plan.Order_No, plan.Customer_Abb, plan.Parts_No, plan.Cost_No, plan.Process_No, plan.Process_Name, plan.ProcessG_Name,
    formatDate(plan.Process_Date),formatDate(plan.Process_Time), plan.Machine_CD, plan.Machine_Name, plan.CostG_CD,plan.Machine_Time, plan.M_Coefficient,plan.Machine_Cost_Time,
    plan.Person_CD, plan.Person_Name, plan.Person_Time, plan.P_Coefficient, plan.Person_Cost_Time, plan.Process_Qty, formatDate(plan.Cs_Complete_Date),
    formatDate(plan.Reg_Date), formatDate(plan.Upd_Date), plan.Pt_Material, plan.Pt_Qty, plan.OdPt_No, plan.OdPtCs_No,formatValue(plan.Pt_Split), plan.Pt_Spare_Qty, plan.Pl_Progress_CD,
    plan.Pl_Schedule_CD, plan.Pt_Instructions, plan.Pt_Remark, plan.Pt_Information, plan.Pl_Reg_Person_CD, formatDates(plan.Pt_Delivery), formatDate(plan.Pt_Complete_Date),
    formatDate(plan.Pt_Confirm_Date), formatValue(plan.Pt_CAT1), formatValue(plan.Pt_CAT2), formatValue(plan.Pt_CAT3), formatValue(plan.Pt_Pending), plan.Max_No, plan.Priority_Rate, plan.Unit_Price, plan.Price_Process_Qty,
    ...Array.from({ length: 36 }, (_, i) => plan[`PPC${i + 1}`]),
    ...Array.from({ length: 36 }, (_, i) => formatDate(plan[`PPD${i + 1}`])),
    ...Array.from({ length: 36 }, (_, i) => formatDate(plan[`RPD${i + 1}`])),
    formatValue(plan.Money_Object)
  ]);

  const csvContent =
    headers.join(",") + "\n" +
    rows.map((row) => row.join(",")).join("\n");

  const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
  const url = URL.createObjectURL(blob);

  const now = new Date();
  const pad = (n) => String(n).padStart(2, "0");
  const fileName = `QE_Od_List_Detail_${now.getFullYear()}-${pad(
    now.getMonth() + 1
  )}-${pad(now.getDate())}_${pad(now.getHours())}-${pad(
    now.getMinutes()
  )}-${pad(now.getSeconds())}.csv`;

  const link = document.createElement("a");
  link.href = url;
  link.setAttribute("download", fileName);
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};


  return (
    <div className="p-8 bg-gray-50 rounded-lg shadow-lg max-w-screen-lg mx-auto">
      <h2 className="text-3xl font-semibold text-blue-600 text-center mb-6">
        QR_CS_List_view
      </h2>

      <div className="flex justify-end mb-4">
        <button
          onClick={exportToCSV}
          className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
        >
          Export CSV
        </button>
      </div>

      <div className="overflow-x-auto">
        <table className="max-w-[1500px] table-auto border-collapse">
          <thead>
            <tr>
              {/* หัวตาราง */}
              <th className="border px-10 py-1 text-left">Order_No</th>
              <th className="border px-10 py-1 text-left">Parts_No</th>
              <th className="border px-10 py-1 text-left">Process_No</th>
              <th className="border px-10 py-1 text-left">Process_Name</th>
              <th className="border px-10 py-1 text-left">ProcessG_Name</th>
              <th className="border px-10 py-1 text-left">CostG_CD</th>
              <th className="border px-10 py-1 text-left">Machine_Cost_Time</th>
              <th className="border px-10 py-1 text-left">Person_Time</th>
              <th className="border px-10 py-1 text-left">P_Coefficient</th>
              <th className="border px-10 py-1 text-left">Person_Cost_Time</th>
              <th className="border px-10 py-1 text-left">Cs_Complete_Date</th>
              {/* เพิ่มหัวตารางอื่นๆ ตามต้องการ */}
            </tr>
          </thead>
          <tbody>
            {displayedData.length > 0 ? (
              displayedData.map((item, index) => (
                <tr key={index} className="hover:bg-gray-100">
                  {/* แสดงข้อมูลจากตาราง */}
                  <td className="border px-4 py-2">
                    {(item.Order_No)}
                  </td>
                  <td className="border px-4 py-2">
                    {(item.Parts_No)}
                  </td>
                  <td className="border px-4 py-2">
                    {(item.Process_No)}
                  </td>
                  <td className="border px-4 py-2">
                    {(item.Process_Name)}
                  </td>
                  <td className="border px-4 py-2">
                    {(item.ProcessG_Name)}
                  </td>
                  <td className="border px-4 py-2">
                    {(item.CostG_CD)}
                  </td>
                  <td className="border px-4 py-2">
                    {(item.Machine_Cost_Time)}
                  </td>
                  <td className="border px-4 py-2">
                    {(item.Person_Time)}
                  </td>
                  <td className="border px-4 py-2">
                    {(item.P_Coefficient)}
                  </td>
                  <td className="border px-4 py-2">
                    {(item.Person_Cost_Time)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDateUTC(item.Cs_Complete_Date)}
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan="3" className="border px-4 py-2 text-center">
                  No data available
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      <div className="flex justify-between items-center mt-4">
        <button
          onClick={goToPrevPage}
          disabled={currentPage === 1}
          className={`p-2 rounded-full ${
            currentPage === 1
              ? "bg-gray-300 cursor-not-allowed"
              : "bg-blue-500 text-white hover:bg-blue-600"
          }`}
        >
          <HiChevronLeft size={20} />
        </button>

        <div className="flex items-center gap-4">
          <span>
            Page {currentPage} of {totalPages || 1}
          </span>
          <select
            className="border border-gray-400 rounded px-2 py-1"
            value={rowsPerPage}
            onChange={(e) => {
              setRowsPerPage(Number(e.target.value));
              setCurrentPage(1);
            }}
          >
            <option value={10}>10 Rows</option>
            <option value={15}>15 Rows</option>
            <option value={20}>20 Rows</option>
            <option value={25}>25 Rows</option>
          </select>
        </div>

        <button
          onClick={goToNextPage}
          disabled={currentPage === totalPages || totalPages === 0}
          className={`p-2 rounded-full ${
            currentPage === totalPages || totalPages === 0
              ? "bg-gray-300 cursor-not-allowed"
              : "bg-blue-500 text-white hover:bg-blue-600"
          }`}
        >
          <HiChevronRight size={20} />
        </button>
      </div>
    </div>
  );
}
