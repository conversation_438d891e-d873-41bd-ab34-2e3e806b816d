/**
 * Get the appropriate exit destination based on user's Worker_Menu
 * @param {Object} authUser - The authenticated user object
 * @returns {string} - The route to navigate to
 */
export const getExitDestination = (authUser) => {
  if (!authUser || !authUser.Worker_Menu) {
    return "/dashboard"; // Default for users without Worker_Menu
  }

  const workerMenu = authUser.Worker_Menu.toString();
  
  switch (workerMenu) {
    case "5":
      return "/production";
    case "6":
      return "/qc";
    default:
      return "/dashboard";
  }
};
