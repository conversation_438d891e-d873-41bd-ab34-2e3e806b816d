import React, { useState, useEffect, useRef } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useOrder } from "../hooks/use-order";
import { usePurchase } from "../hooks/use-purchase";
import { usePlan } from "../hooks/use-plan";
import { useCost } from "../hooks/use-cost";
import Swal from "sweetalert2";
import Sidebar from "./Sidebar";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { parse, isValid, addHours } from "date-fns";
import Select from "react-select";
import axios from "axios";

// Icons
import { AiTwotoneCalendar } from "react-icons/ai";
import { FaEye, FaEyeSlash } from "react-icons/fa";

const CustomInput = React.forwardRef(({ value, onClick }, ref) => (
  <div className="relative">
    <input
      onClick={onClick}
      ref={ref}
      value={value}
      readOnly
      className="border rounded px-2 py-1 text-xs w-[100px]"
    />
    <AiTwotoneCalendar
      className="absolute right-2 top-1/2 transform -translate-y-1/2 text-2xl text-gray-400 cursor-pointer"
      onClick={onClick}
      size={16}
    />
  </div>
));
export default function PlanInfo() {
  const apiUrl_4000 = import.meta.env.VITE_BACKEND_URL;
  const apiUrl_5173 = import.meta.env.VITE_FRONTEND_URL;

  const navigate = useNavigate();
  const location = useLocation();
  const SearchPartsNoRef = useRef(null);
  const { searchOrderNo: initialSearchOrderNo = "" } = location.state || {};
  const [searchOrderNo, setSearchOrderNo] = useState(initialSearchOrderNo);
  const [isSave, setIsSave] = useState(false);
  const [lastMinute, setLastMinute] = useState(null);
  const [searchPlanNo, setSearchPlanNo] = useState("");
  const [autoYearChange, setAutoYearChange] = useState(true);
  const planDeliDateRef = useRef(null);
  const productDeliDateRef = useRef(null);
  const confirmDeliDateRef = useRef(null);
  const planDateRef = useRef(null); // ใช้ ref สำหรับ plan date
  const resultDateRef = useRef(null); // ใช้ ref สำหรับ result date
  const [odDates, setOdDates] = useState({
    productDeliDate: null,
    confirmDeliDate: null,
  });
  const [dates, setDates] = useState({
    // ฟิลด์สำหรับ Date Range
    startDate: null,
    endDate: null,
    planDeliDate: null,
    // ฟิลด์สำหรับหลายๆ วันที่/เวลา
    planRegDate: null,
    ptCompDate: null,
    qcCompDate: null,
    planUpdDate: null,
  });
  const {
    orderData,
    searchOrderData,
    setOrderData,
    WorkgData,
    WorkerData,
    setWorkerData,
    SpecificData,
    setSpecificData,
    CustomerData,
    setCustomerData,
    Request3Data,
    CoatingData,
    setCoatingData,
    OdProgressData,
    setOdProgressData,
    DeliveryData,
    setDeliveryData,
    PriceData,
    TargetData,
    setTargetData,
    SupplyData,
    updateOrderpl,
  } = useOrder();
  const {
    planData,
    setPlanData,
    selectedPlanNo,
    setSelectedPlanNo,
    searchPartsData,
    qmprocessData,
    processData,
    plprogressData,
    setPlProgressData,
    ScheduleData,
    setScheduleData,
    PartsData,
    UnitsData,
    createPlan,
    createSchedule,
    createResult,
    createWip,
    selectPartsData,
    deleteResult,
    deletePlan,
    deleteSchedule,
    deleteWip,
    hasPlanData,
    Schedule_Calc,
    Schedule_Bk,
    Schedule_Re,
    update_temdate,
    ProcessSheetAllPlanData,
    fetchProcessSheetAllPlan,
    ProcessSheet1PData,
    fetchProcessSheet1P,
    ProcessGData,
    QR_ProG_Plan,
    hasConnectData,
    ConnectData,
    QuotleData,
    setPartsData,
    Quote_Info_View,
    Execute,
    ConfirmExecute,
    Money_Object,
    StatusData,
  } = usePlan();
  const [isF3Pressed, setIsF3Pressed] = useState(false);
  const { ProcessCData } = useCost();
  const { purchaseData, setPurchaseData } = usePurchase();
  const [selectedSalesPersonAbb, setSelectedSalesPerson] = useState("");
  const [SpecificName, setSpecificName] = useState("");
  const [selectedCustomerAbb, setSelectedCustomerAbb] = useState("");
  const [coatingName, setCoatingName] = useState("");
  const [OdProgressName, setOdProgressName] = useState("");
  const [DeliveryName, setDeliveryName] = useState("");
  const [PriceName, setPriceName] = useState("");
  const [UnitName, setUnitName] = useState("");
  const [targetName, setTargetName] = useState("");
  const [ProgressName, setProgressName] = useState("");
  const [Person_Name, setPerson_Name] = useState("");
  const [updPerson_Name, setupdPerson_Name] = useState("");
  const [Schedule_Name, setSchedule_Name] = useState("");
  const [Stagnat_Scale, setStagnat_Scale] = useState("");
  const [ManHour_Scale, setManHour_Scale] = useState("");
  const [Search_Odpt_No, setSearch_Odpt_No] = useState("");
  const RegPerson = useRef(null);
  const PartsNo = useRef(null);
  const SearchorderNoRef = useRef(null);
  const [isPlanDateEditable, setIsPlanDateEditable] = useState(false);
  const [isResultDateEditable, setIsResultDateEditable] = useState(false);
  const [isDatePickerDisabled, setIsDatePickerDisabled] = useState(true);
  const [isEditedOrderDate, setIsEditedOrderDate] = useState(false);
  const [showPopup, setShowPopup] = useState(false);
  const [Line_Edit_View, setLine_Edit_View] = useState(false);
  const [selectedId, setSelectedId] = useState(null);
  const [selectedMates, setSelectedMates] = useState("");
  const [previousProductDeliDate, setPreviousProductDeliDate] = useState(null);
  const [previousConfirmDeliDate, setPreviousConfirmDeliDate] = useState(null);
  const [hasUserEditedOrderNo, setHasUserEditedOrderNo] = useState(false);

  const [buttonState, setButtonState] = useState({
    F1: false,
    F2: false,
    F3: false,
    F4: false,
    F5: false,
    F6: false,
    F7: false,
    F8: false,
    F9: false,
    F10: false,
    F11: false,
    F12: true,
    ScheduleCalc: false,
    Quoteinfo: true,
  });

  const inputs = Array.from({ length: 36 }, (_, i) => i + 1);

  const handleInputChange = async (event, isPurchase, isPlan = false) => {
    const { id, value, type, checked } = event.target;

    if (isPurchase) {
      setPurchaseData((prevPurchaseData) => ({
        ...prevPurchaseData,
        [id]: type === "checkbox" ? checked : value === "" ? null : value,
      }));
    } else if (isPlan) {
      setPlanData((prevPlanData) => ({
        ...prevPlanData,
        [id]: type === "checkbox" ? checked : value === "" ? null : value,
      }));
    } else {
      setOrderData((prevOrderData) => ({
        ...prevOrderData,
        [id]: type === "checkbox" ? checked : value === "" ? null : value,
      }));
    }

    if (id === "Search_Order_No") {
      setHasUserEditedOrderNo(true);
      setSearchOrderNo(value);
      setIsEditedOrderDate(true);
    }
  };

  window.addEventListener("beforeunload", function () {
    localStorage.removeItem("isF6Clicked");
    localStorage.removeItem("isF7Clicked");
  });

  useEffect(() => {
    if (searchPlanNo) {
      setSearch_Odpt_No(`${searchOrderNo || ""}${searchPlanNo}`);
    }
  }, [searchOrderNo, searchPlanNo]);

  useEffect(() => {
    const handleMessage = (event) => {
      if (event.data?.type === "REFRESH_SEARCH") {
        setSearchOrderNo((prev) => `${prev} `); // อัปเดตค่าเล็กน้อยเพื่อให้ useEffect ทำงาน
      }
    };

    window.addEventListener("message", handleMessage);
    return () => {
      window.removeEventListener("message", handleMessage);
    };
  }, []);

  const handleSearch = async () => {
    if (!searchOrderNo.trim()) return; // ถ้าไม่มีค่าก็ไม่ต้องเรียก API

    const result = await searchOrderData(searchOrderNo);
    if (result) {
      searchPartsData(searchOrderNo);

      if (SearchPartsNoRef.current) {
        SearchPartsNoRef.current.focus();
        setTimeout(() => {
          SearchPartsNoRef.current.openMenu();
        }, 100);
      }

      setButtonState((prevState) => ({
        ...prevState,
        F1: true,
        F3: true,
        F4: true,
        F5: true,
        F6: true,
        F7: false,
        F8: true,
        F11: true,
      }));
    } else {
      setButtonState((prevState) => ({
        ...prevState,
        F1: false,
        F3: false,
        F4: false,
        F5: false,
        F6: false,
        F7: false,
        F8: false,
        F11: false,
      }));
      Swal.fire({
        title: "ไม่พบข้อมูล",
        html: `${searchOrderNo} is not yet registered !<br>${searchOrderNo} ที่ป้อนไปยังไม่ได้ถูกลงทะเบียน !<br>${searchOrderNo} は登録されていません!`,
        icon: "warning",
        confirmButtonText: "ตกลง",
      });
      setSearchOrderNo(value);

      if (value) {
        const result = await searchOrderData(value);

        if (result) {
          searchPartsData(value);

          setButtonState((prevState) => ({
            ...prevState,
            F1: true,
            F3: true,
            F4: true,
            F5: true,
            F6: true,
            F7: false,
            F8: true,
            F11: true,
            ScheduleCalc: true,
            Quoteinfo: false,
          }));
        } else {
          Swal.fire({
            title: "ไม่พบข้อมูล",
            html: `${value} is not yet registered!`,
            icon: "warning",
            confirmButtonText: "ตกลง",
          });

          setButtonState((prevState) => ({
            ...prevState,
            F1: false,
            F3: false,
            F4: false,
            F5: false,
            F6: false,
            F7: false,
            F8: false,
            F11: false,
            ScheduleCalc: false,
            Quoteinfo: false,
          }));
        }
      }
    }
  };

  const handleSearchOrderNoBlur = async (event) => {
    const value = event.target.value;
    setSearchOrderNo(value);  // อัปเดตค่าก่อนเรียก search เพื่อให้ state ถูกต้อง

    if (value) {
      await handleSearch();  // ใช้ logic เดียวกัน ไม่ต้องเขียนซ้ำ
    }
  };

  const calculateAmount = async () => {
    let amount = 0; // กำหนดค่าเริ่มต้น

    if (!orderData) {
      // console.warn("No order data found for Order_No:", planData?.Order_No);
    } else {
      const unitPrice = orderData?.Unit_Price || 0;
      const ptQty = planData?.Pt_Qty || 0;
      const ptSpareQty = planData?.Pt_Spare_Qty || 0;
      const ptNgQty = planData?.Pt_NG_Qty || 0;

      if (planData?.Money_Object === true) {
        if (ptNgQty > 0 && ptSpareQty > 0) {
          amount = unitPrice * ptQty;
        } else if (ptNgQty > 0) {
          amount = unitPrice * (ptQty - ptNgQty);
        } else {
          amount = unitPrice * ptQty;
        }
      }
    }

    return (Math.ceil(amount * 100) / 100).toFixed(2); // ปัดขึ้นและแสดงทศนิยม 2 ตำแหน่ง
  };

  //fix
  const handleMoneyObjectChange = async (event) => {
    const value = event.target.checked;
    const amount = await calculateAmount();
    setPlanData((prevData) => ({
      ...prevData,
      Money_Object: value,
      Amount: amount || "0.00",
    }));
  };

  const handleDateChange = (field, value) => {
    if (!value) {
      setDates((prev) => ({ ...prev, [field]: "" }));
      return;
    }

    const y = value.getFullYear();
    const m = String(value.getMonth() + 1).padStart(2, "0");
    const d = String(value.getDate()).padStart(2, "0");

    const dateStr = `${y}-${m}-${d}`; // 👉 ไม่ใช่ Date object แล้ว

    setDates((prev) => ({
      ...prev,
      [field]: dateStr,
    }));
  };

  const editOrders = async (updatedOrderData) => {
    try {
      const response = await axios.put(
        `${apiUrl_4000}/order/edit-order`,
        updatedOrderData
      );
      // console.log("Order updated successfully:", response.data);
      setOrderData(updatedOrderData);
    } catch (error) {
      console.error(
        "Error updating order:",
        error.response?.data || error.message
      );
      throw new Error("Failed to update order");
    }
  };

  useEffect(() => {
    if (orderData?.Product_Delivery) {
      const parsedDate = new Date(orderData.Product_Delivery);
      setOdDates((prev) => ({
        ...prev,
        productDeliDate: parsedDate,
      }));
      setPreviousProductDeliDate(parsedDate);
    }

    if (orderData?.Confirm_Delivery) {
      const parsedConfirmDeliDate = new Date(orderData.Confirm_Delivery);
      setOdDates((prev) => ({
        ...prev,
        confirmDeliDate: parsedConfirmDeliDate,
      }));
      setPreviousConfirmDeliDate(parsedConfirmDeliDate);
    }
  }, [orderData]);

  const handleProductDeliDateBlur = ({ target: { value } }) => {
    if (!value) {
      setOdDates((prev) => ({
        ...prev,
        productDeliDate: previousProductDeliDate,
      }));
      return;
    }

    let date = parse(value, "dd/MM/yyyy", new Date());

    if (isValid(date)) {
      date = addHours(date, 7);

      if (previousProductDeliDate?.getTime() !== date.getTime()) {
        Swal.fire({
          title: "Order info update?",
          text: "Do you want to update the changes?",
          icon: "question",
          showCancelButton: true,
          confirmButtonText: "Yes",
          cancelButtonText: "No",
        }).then((result) => {
          if (result.isConfirmed) {
            const updatedOrderData = {
              ...orderData,
              Product_Delivery: date.toISOString(),
            };

            if (!updatedOrderData.Order_No) {
              return;
            }

            editOrders(updatedOrderData);
          } else {
            setOdDates((prev) => ({
              ...prev,
              productDeliDate: previousProductDeliDate,
            }));
          }
        });
      }
    } else {
      console.log("Invalid Date:", value);
    }
  };

  const handleConfirmDeliDateBlur = ({ target: { value } }) => {
    if (!value) {
      setOdDates((prev) => ({
        ...prev,
        confirmDeliDate: previousConfirmDeliDate,
      }));
      return;
    }

    let date = parse(value, "dd/MM/yyyy", new Date());

    if (isValid(date)) {
      date = addHours(date, 7);

      if (previousConfirmDeliDate?.getTime() !== date.getTime()) {
        Swal.fire({
          title: "Order info update?",
          text: "Do you want to update the changes?",
          icon: "question",
          showCancelButton: true,
          confirmButtonText: "Yes",
          cancelButtonText: "No",
        }).then((result) => {
          if (result.isConfirmed) {
            const updatedOrderData = {
              ...orderData,
              Confirm_Delivery: date.toISOString(),
            };

            if (!updatedOrderData.Order_No) {
              return;
            }

            editOrders(updatedOrderData);
          } else {
            setOdDates((prev) => ({
              ...prev,
              confirmDeliDate: previousConfirmDeliDate,
            }));
          }
        });
      }
    } else {
      console.log("Invalid Date:", value);
    }
  };

  const handleOpenDatePicker = (ref) => {
    if (ref.current) {
      ref.current.setOpen(true); // เปิด DatePicker ถ้า ref.current มีค่า
    }
  };

  const formatDate = (date) => {
    if (!date) return "";

    const d = new Date(date);

    // ดึงค่าแบบ UTC แล้วบวก 7 ชั่วโมง
    const year = d.getUTCFullYear();
    const month = d.getUTCMonth();
    const day = d.getUTCDate();
    const hours = d.getUTCHours();
    const minutes = d.getUTCMinutes();
    const seconds = d.getUTCSeconds();

    // สร้างวันที่ใหม่หลังจากบวก 7 ชั่วโมง
    const thaiDate = new Date(
      Date.UTC(year, month, day, hours + 7, minutes, seconds)
    );

    const dd = String(thaiDate.getUTCDate()).padStart(2, "0");
    const mm = String(thaiDate.getUTCMonth() + 1).padStart(2, "0");
    const yyyy = thaiDate.getUTCFullYear();

    return `${dd}/${mm}/${yyyy}`;
  };

  const formatDateToISO8 = (dateString) => {
    const date = new Date(dateString);

    // แปลงเวลาเป็น UTC+8
    const utc8Offset = 8 * 60 * 60 * 1000;
    const dateInUTC8 = new Date(date.getTime() + utc8Offset);

    // ดึงค่า ปี / เดือน / วัน
    const yyyy = dateInUTC8.getFullYear();
    const mm = String(dateInUTC8.getMonth() + 1).padStart(2, "0");
    const dd = String(dateInUTC8.getDate()).padStart(2, "0");

    return `${yyyy}/${mm}/${dd}`;
  };

  const handlePlanInputChange = async (
    event,
    selectedOption = null,
    inputId = null,
    dateValue = null
  ) => {
    if (dateValue && inputId) {
      // ✅ สร้างวันที่แบบ UTC 00:00:00
      const utcDate = new Date(
        Date.UTC(
          dateValue.getFullYear(),
          dateValue.getMonth(),
          dateValue.getDate()
        )
      );

      const formattedDate = utcDate.toISOString(); // ✅ ใช้ utcDate

      setPlanData((prevPlanData) => ({
        ...prevPlanData,
        [inputId]: formattedDate,
      }));
      return;
    }
    if (selectedOption && inputId) {
      const newValue = selectedOption?.value || "";

      // กรณีพิเศษ: Search_Parts_No
      if (inputId === "Search_Parts_No") {
        setSearchPlanNo(newValue);
        setHasUserEditedOrderNo(true);

        if (newValue) {
          setButtonState((prevState) => ({
            ...prevState,
            F1: false,
            F2: true,
            F3: true,
            F6: false,
            F7: true,
            F10: true,
            F11: true,
            ScheduleCalc: true,
            Quoteinfo: false,
          }));
        }
      }

      // ✅ อัปเดต planData สำหรับ inputId อื่นๆ
      setPlanData((prevPlanData) => ({
        ...prevPlanData,
        [inputId]: newValue,
      }));

      return;
    }

    if (inputId && dateValue !== undefined) {
      const utcDate = dateValue
        ? new Date(
            Date.UTC(
              dateValue.getFullYear(),
              dateValue.getMonth(),
              dateValue.getDate()
            )
          )
        : null;

      const formattedDate = utcDate ? utcDate.toISOString() : null;

      setPlanData((prev) => ({
        ...prev,
        [inputId]: formattedDate,
      }));
      return;
    }
    if (!event || !event.target) return;

    const { id, value, type, checked } = event.target;
    let formattedValue = value;

    if (type === "datetime-local" && value) {
      const dateWithCurrentTime = new Date(value);
      const currentMinute = dateWithCurrentTime.getMinutes();

      if (lastMinute !== null && currentMinute !== lastMinute) {
        event.target.blur();
      }

      setLastMinute(currentMinute);
      formattedValue = dateWithCurrentTime.toISOString();
    }

    const amount = await calculateAmount();

    setPlanData((prevPlanData) => {
      // หากเป็น checkbox ที่เป็น Edit_CAT ให้ทำให้ตัวเดียวที่เลือกเป็น true และตัวอื่น false
      if (["Edit_CAT1", "Edit_CAT2", "Edit_CAT3", "Edit_CAT4"].includes(id)) {
        return {
          ...prevPlanData,
          Edit_CAT1: false,
          Edit_CAT2: false,
          Edit_CAT3: false,
          Edit_CAT4: false,
          [id]: checked, // ตัวที่ถูกคลิกจะถูกตั้งตามค่า checked
          Amount: amount,
        };
      } else {
        // กรณีที่ไม่ใช่ checkbox Edit_CAT
        return {
          ...prevPlanData,
          [id]:
            type === "checkbox"
              ? checked
              : type === "date" && value !== ""
              ? new Date(`${value}T00:00:00.000Z`).toISOString()
              : value === ""
              ? null
              : value,
          Amount: amount,
        };
      }
    });

    if (id === "Search_Parts_No") {
      setSearchPlanNo(value);

      if (value) {
        setSearch_Odpt_No(`${searchOrderNo || ""}${value}`);
        setButtonState((prevState) => ({
          ...prevState,
          F1: false,
          F2: true,
          F3: true,
          F7: true,
          F10: true,
          F11: true,
          ScheduleCalc: true,
          Quoteinfo: false,
        }));
      } else {
        setButtonState((prevState) => ({
          ...prevState,
          F1: false,
          F2: false,
          F3: false,
          F7: false,
          F10: false,
          F11: false,
        }));
      }
    }
  };

  const handlePPVChange = (id, value) => {
    setPlanData((prev) => ({
      ...prev,
      [`PPV${id}`]: value,
    }));
  };

  const handleSearch_Order_NoChange = async (newOrder_No) => {
    if (newOrder_No && newOrder_No.trim() !== "") {
      await searchOrderData(newOrder_No);
    } else {
      // เมื่อ newOrder_No ว่าง เคลียร์ข้อมูลทั้งหมด
      setOrderData("");
      setSelectedSalesPerson("");
      setSelectedCustomerAbb("");
      setSpecificName("");
      setOdProgressName("");
      setCoatingName("");
      setDeliveryName("");
      setPriceName("");
      setUnitName("");
      setTargetName("");
      setProgressName("");
      setPerson_Name("");
      setupdPerson_Name("");
      setSchedule_Name("");
      setStagnat_Scale("");
      setManHour_Scale("");
      setSearch_Odpt_No("");
      setPlanData("");
      setSelectedPlanNo([]);
      setIsEditedOrderDate(false);

      setOdDates((prev) => ({
        ...prev,
        productDeliDate: null,
        confirmDeliDate: null,
      }));
    }
  };

  useEffect(() => {
    if (!searchOrderNo || searchOrderNo.trim() === "") {
      // เมื่อ searchOrderNo ว่าง ให้เรียกเคลียร์ข้อมูล
      handleSearch_Order_NoChange("");
    }
  }, [searchOrderNo]);

  useEffect(() => {
    return () => {
      // Cleanup: clear data on unmount
      setOrderData({});
      setPlanData({});
    };
  }, []);

  useEffect(() => {
    // handleSearch_Order_NoChange();
    if (searchOrderNo && searchPlanNo) {
      setButtonState({
        F1: false,
        F2: true,
        F3: true,
        F4: true,
        F5: true,
        F6: true,
        F7: true,
        F8: true,
        F9: false,
        F10: true,
        F11: true,
        ScheduleCalc: true,
        Quoteinfo: false,
      });
    } else if (searchOrderNo && searchPlanNo === "") {
      setButtonState({
        F1: true,
        F2: false,
        F3: true,
        F4: true,
        F5: true,
        F6: true,
        F7: false,
        F8: true,
        F9: false,
        F10: true,
        F11: true,
      });
    } else if (searchOrderNo === "" && searchPlanNo === "") {
      setButtonState({
        F1: false,
        F2: false,
        F3: false,
        F4: false,
        F5: false,
        F6: false,
        F7: false,
        F8: false,
        F9: false,
        F10: false,
        F11: false,
        ScheduleCalc: false,
      });
    }
  }, [searchOrderNo]);

  // const handleCheckboxChange = (event) => {
  //   const { id, checked } = event.target;
  //   setCheckboxStates((prevState) => ({
  //     ...prevState,
  //     [id]: checked,
  //   }));
  //   console.log(id, checked);
  // };

  const handleF2Click = () => {
    try {
      searchPermission(false);
      editPermission(true);
      if (RegPerson.current) {
        RegPerson.current.focus();
      }
      setButtonState((prevState) => ({
        ...prevState,
        F1: false,
        F2: false,
        F3: false,
        F4: false,
        F5: false,
        F6: false,
        F7: false,
        F8: true,
        F9: true,
        F10: false,
        F11: true,
        F12: true,
        ScheduleCalc: false,
        Quoteinfo: true,
      }));

      setIsPlanDateEditable(true);
      setIsResultDateEditable(true);
      setIsDatePickerDisabled(false);
    } catch (error) {
      alert("Error occurs when F2_Click\nPlease contact system administrator.");
    }
  };

  const MaterialOptions = [
    "Material1",
    "Material2",
    "Material3",
    "Material4",
    "Material5",
  ];

  const handleF3Click = () => {
    searchPermission(false);
    editPermission(true);
    setShowPopup(true);
    setButtonState({
      F1: true,
      F2: true,
      F3: true,
      F4: true,
      F5: true,
      F6: true,
      F7: false,
      F8: false,
      F9: false,
      F10: false,
      F11: false,
      F12: true,
      ScheduleCalc: false,
      Quoteinfo: true,
    });

    try {
      // ดึงข้อมูลผู้ใช้จาก localStorage
      const userData = JSON.parse(localStorage.getItem("user"));
      const loggedInWorkerCD = userData?.Worker_CD || "";
      const loggedInWorkerName = userData?.Worker_Abb || "";

      setPlanData((prevData) => {
        // กำหนดค่า PPD1 - PPD36 และ RPD1 - RPD36 เป็น null
        const ppdRpdData = {};
        for (let i = 1; i <= 36; i++) {
          ppdRpdData[`PPD${i}`] = null;
          ppdRpdData[`RPD${i}`] = null;
        }

        const updatedData = {
          ...Object.fromEntries(
            Object.keys(prevData).map((key) => [
              key,
              typeof prevData[key] === "boolean" ? false : "",
            ])
          ),
          ...ppdRpdData, // รวมค่า PPD และ RPD ที่ตั้งค่าเป็น null
          Pl_Progress_CD: "0",
          Pl_Quote_CD: "0",
          Max_No: "0",
          Order_No: prevData?.Order_No || orderData?.Order_No || "",
          Parts_CD:
            Array.isArray(PartsData) && PartsData.length > 0
              ? PartsData[5].Parts_CD //[0-4] เป็นภาษาญี่ปุ่น base เริ่มที่ [5]
              : "",
          Pt_Qty: orderData?.Quantity ?? "", // ตั้งค่า Pt_Qty เป็นค่า Quantity
          Pt_Unit_CD: orderData?.Unit_CD || "",
          Pt_Spare_Qty: "0",
          Pt_NG_Qty: "0", // นำค่า NG_Qty จาก orderData มาใช้
          Pl_Reg_Person_CD: loggedInWorkerCD,
          Pl_Reg_Person_Name: loggedInWorkerName,
          Pl_Ed_Rev_Day: null,
          Pl_St_Rev_Day: null,
        };

        return updatedData;
      });

      // ตั้งค่าค่า planDeliDate ให้ตรงกับ Request_Delivery
      if (orderData?.Request_Delivery) {
        setDates((prevDates) => ({
          ...prevDates,
          planDeliDate: new Date(orderData.Request_Delivery),
        }));
      }

      searchPermission(false);
      editPermission(true);
      setPerson_Name(loggedInWorkerName);
      setProgressName("");
      setupdPerson_Name("");
      setIsPlanDateEditable(true);
      setIsResultDateEditable(true);
      setIsDatePickerDisabled(false);
      setButtonState((prevState) => ({
        ...prevState,
        F1: false,
        F2: false,
        F3: false,
        F4: false,
        F5: false,
        F6: false,
        F9: true,
        F11: true,
        F12: false,
      }));

      if (PartsNo.current) {
        PartsNo.current.focus();
      }
    } catch (error) {
      console.error("Error in handleF3Click:", error);
      Swal.fire({
        title: "An error occurred.",
        text: "Please contact the administrator.",
        icon: "error",
        confirmButtonText: "OK",
      });
    }
  };

  const handleCheckboxChange = (e) => {
    setSelectedMates(e.target.value);
  };

  const handleConfirm = () => {
    const selectedValue = orderData?.[selectedMates] ?? "";
    setPlanData((prevData) => ({
      ...prevData,
      Pt_Material: selectedValue.trim() !== "" ? selectedValue : "",
    }));
    setShowPopup(false);
  };

  const handleF4Click = async () => {
    try {
      const orderExists = await searchOrderData(searchOrderNo);
      if (orderExists) {
        navigate("/purchase-info", { state: { searchOrderNo } });
      } else {
        await Swal.fire({
          title: "The information is incorrect.",
          text: "No order number found",
          icon: "warning",
          confirmButtonText: "OK",
        });
      }
    } catch (error) {
      alert("Error occurs when F4_Click\nPlease contact system administrator.");
    }
  };

  useEffect(() => {
    if (orderData?.Order_No) {
      fetchProcessSheetAllPlan(orderData.Order_No);
    }
  }, [orderData?.Order_No]);

  const handleF6Click = async () => {
    try {
      if (!orderData?.Order_No) {
        return Swal.fire({
          icon: "warning",
          title: "Order_No is required",
        });
      }

      // แสดง Loading ทันที
      const loadingPopup = Swal.fire({
        title: "Loading...",
        text: "Generating report...",
        showConfirmButton: false,
        allowOutsideClick: false,
        didOpen: () => {
          Swal.showLoading();
        },
      });

      // เรียกข้อมูล
      const success = await fetchProcessSheetAllPlan(orderData.Order_No);
      if (!success) {
        Swal.close();
        return Swal.fire({
          icon: "error",
          title: "No Plan Data!",
        });
      }

      const reportUrl = `${apiUrl_5173}/reports/RD_Process_Sheet_All_Plan/${orderData.Order_No}`;

      // preload iframe ซ่อน
      const iframe = document.createElement("iframe");
      iframe.src = reportUrl;
      iframe.style.width = "0";
      iframe.style.height = "0";
      iframe.style.border = "none";
      iframe.style.position = "absolute";
      iframe.style.visibility = "hidden";

      iframe.onload = () => {
        iframe.contentWindow.postMessage(
          {
            OrderNo: orderData.Order_No,
            OrderData: orderData,
            connectPtAbb: Connect_Pt_Abb,
            connectPrAbb: Connect_Pr_Abb,
            CustomerData,
            ProcessSheetAllPlanData,
          },
          `${apiUrl_5173}`
        );

        // ปิด popup เมื่อโหลดเสร็จ
        Swal.close();
      };

      document.body.appendChild(iframe);
    } catch (error) {
      console.error("Error in handleF6Click:", error);
      Swal.fire({
        icon: "error",
        title: "Unexpected error occurred!",
      });
    }
  };

  useEffect(() => {
    if (orderData?.Order_No && planData?.Parts_No) {
      fetchProcessSheet1P(orderData.Order_No, planData.Parts_No);
    }
  }, [orderData?.Order_No, planData?.Parts_No]);

  const handleF7Click = async () => {
    try {
      if (!orderData?.Order_No || !planData?.Parts_No) {
        Swal.fire({
          icon: "warning",
          title: !orderData?.Order_No
            ? "Order_No is required"
            : "Parts_No is required",
        });
        return;
      }

      // แสดง popup โหลดก่อน
      const loadingPopup = Swal.fire({
        title: "Loading...",
        text: "Generating report...",
        showConfirmButton: false,
        allowOutsideClick: false,
        didOpen: () => {
          Swal.showLoading();
        },
      });

      // ดึงข้อมูลทุกตัวล่วงหน้า (fetchProcessSheet1P ต้องคืน true/false)
      const success = await fetchProcessSheet1P(
        orderData.Order_No,
        planData.Parts_No
      );
      if (!success) {
        Swal.close();
        Swal.fire({
          icon: "error",
          title: "No Plan Data!",
        });
        return;
      }

      const reportUrl = `${apiUrl_5173}/reports/RD_Process_Sheet_1P/${orderData.Order_No}/${planData.Parts_No}`;

      // preload iframe
      const iframe = document.createElement("iframe");
      iframe.src = reportUrl;
      iframe.style.width = "0";
      iframe.style.height = "0";
      iframe.style.border = "none";
      iframe.style.position = "absolute";
      iframe.style.visibility = "hidden";

      iframe.onload = () => {
        iframe.contentWindow.postMessage(
          {
            orderNo: orderData.Order_No,
            partNo: planData.Parts_No,
            connectPtAbb: Connect_Pt_Abb,
            connectPrAbb: Connect_Pr_Abb,
            OrderData: orderData,
            CustomerData,
            ProcessSheet1PData,
            ProcessSheetAllPlanData,
          },
          `${apiUrl_5173}`
        );

        // ปิด loading หลังจาก iframe โหลดเสร็จจริง
        Swal.close();
      };

      document.body.appendChild(iframe);
    } catch (error) {
      console.error("Error in handleF7Click:", error);
      Swal.fire({
        icon: "error",
        title: "Unexpected error occurred",
      });
    }
  };

  const handleF8Click = async () => {
    try {
      const confirm = await Swal.fire({
        title: "Confirm",
        text: "Another Parts_No input?",
        icon: "question",
        showCancelButton: true,
        confirmButtonText: "Yes",
        cancelButtonText: "No",
      });

      if (confirm.isConfirmed) {
        searchPermission(false);
        editPermission(false);
        const planExists = await searchPartsData(searchOrderNo);
        if (planExists) {
          searchPermission(true);
          setSearchPlanNo("");
          setTimeout(() => {
            const selectElement = document.getElementById("Search_Parts_No");
            if (selectElement) {
              selectElement.focus();
            }
          }, 300);
        }
        setButtonState((prevState) => ({
          ...prevState,
          F1: true,
          F3: true,
          F4: true,
          F5: true,
        }));
      }
    } catch (error) {
      console.error("Error in handleF8Click:", error);
      Swal.fire({
        title: "An error occurred.",
        text: "Please contact the administrator.",
        icon: "error",
        confirmButtonText: "OK",
      });
    }
  };

  useEffect(() => {
    const saveData = async () => {
      if (isSave) {
        try {
          await editOrders(orderData);
          await createResult();
          await createPlan();
          await createSchedule();
          await createWip();
          await searchOrderData(searchOrderNo);
          await selectPartsData(orderData?.Order_No, planData?.Parts_No);
        } catch (error) {
          console.error("Error saving data:", error);
        } finally {
          setIsSave(false);
        }
      }
    };
    saveData();
  }, [isSave, planData]);

  const handleF9Click = async () => {
    const updateUIState = () => {
      // อัปเดต UI ตามที่ต้องการ
      setButtonState((prevState) => ({
        ...prevState,
        F1: true,
        F2: true,
        F3: true,
        F4: true,
        F5: true,
        F6: true,
        F7: true,
        F8: true,
        F9: false,
        F12: true,
        ScheduleCalc: true,
        Quoteinfo: false,
      }));
    };
    try {
      const userConfirmed = await Swal.fire({
        title: `Do you change Save On ?`,
        icon: "question",
        showCancelButton: true,
        confirmButtonText: "Yes",
        cancelButtonText: "Cancle",
      });

      if (!userConfirmed.isConfirmed) return;

      // เซฟค่า Pt_Delivery ที่ได้จาก DatePicker
      setPlanData((prevData) => ({
        ...prevData,
        Pt_Delivery: dates.planDeliDate ? dates.planDeliDate : null,
      }));

      setDates((prevData) => ({
        ...prevData,
        planDeliDate: dates.planDeliDate,
      }));
      const Money = await Money_Object();

      if (Money?.status === "question") {
        const MoneyConfirmed = await Swal.fire({
          title: "question",
          text: Money.message || "No message available",
          icon: "question",
          showCancelButton: true,
          confirmButtonText: "Yes",
          cancelButtonText: "Cancel",
        });

        if (MoneyConfirmed.isConfirmed) {
          setPlanData((prevData) => ({
            ...prevData,
            Money_Object: !prevData?.Money_Object,
          }));
        }
      }

      const user = JSON.parse(localStorage.getItem("user"));
      if (user && user.Worker_CD) {
        setPlanData((prev) => ({
          ...prev,
          Pl_Upd_Person_CD: user.Worker_CD,
        }));
      }

      // บันทึกข้อมูล
      setIsSave(true);

      // อัปเดต UI
      updateUIState();
      editPermission(false);
      setActiveRowIndex(null);
      // setIsF9Clicked(true);

      await Swal.fire({
        title: "Update successful!",
        text: "The data has been successfully updated.",
        icon: "success",
        confirmButtonText: "OK",
      });
    } catch (error) {
      console.error("Error in handleF9Click:", error);
      await Swal.fire({
        title: "An error occurred.",
        text: "กรุณาติดต่อผู้ดูแลระบบ",
        icon: "error",
        confirmButtonText: "OK",
      });
    }
  };

  const handleF10Click = async () => {
    try {
      // ตรวจสอบว่า planData มีข้อมูล Order_No และ Parts_No หรือไม่
      if (!planData?.Order_No || !planData?.Parts_No) {
        Swal.fire({
          icon: "error",
          title: "An error occurred.",
          text: "The information you want to delete is not found. Please check the information before proceeding.",
        });
        return;
      }

      // แสดงกล่องยืนยันการลบข้อมูล
      const result = await Swal.fire({
        title: "Confirm deletion?",
        text: "Do you want to delete this information?",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes!",
        cancelButtonText: "Cancle",
      });

      if (result.isConfirmed) {
        const orderExists = await searchPartsData(searchOrderNo);
        editPermission(false);
        searchPermission(true);
        if (orderExists) {
          setButtonState((prevState) => ({
            ...prevState,
            F6: true,
          }));
        } else {
          const odProgressCD = await searchOrderData(searchOrderNo);

          if (odProgressCD < 7) {
            setOrderData((prevOrderData) => ({
              ...prevOrderData,
              Od_Progress_CD: 0,
            }));

            setButtonState((prevState) => ({
              ...prevState,
              F2: false,
              F6: false,
              F8: false,
              Quoteinfo: false,
            }));
          }
        }

        await deleteWip(planData.Order_No, planData?.Parts_No);
        await deleteSchedule(planData.Order_No, planData?.Parts_No);
        await deletePlan(planData.Order_No, planData?.Parts_No);
        await deleteResult(planData.Order_No, planData?.Parts_No);

        Swal.fire(
          "Deleted successfully!",
          "Your information has been successfully deleted..",
          "success"
        );

        setDates((prevDates) => ({
          ...prevDates,
          planDeliDate: null,
        }));

        setButtonState((prevState) => ({
          ...prevState,
          F1: true,
          F7: false,
          F11: true,
          Quoteinfo: false,
        }));

        setOrderData("");
        setSelectedSalesPerson("");
        setSelectedCustomerAbb("");
        setCoatingName("");
        setDeliveryName("");
        setPriceName("");
        setUnitName("");
        setTargetName("");
        setProgressName("");
        setPerson_Name("");
        setupdPerson_Name("");
        setSchedule_Name("");
        setStagnat_Scale("");
        setManHour_Scale("");
        setPlanData("");
        setSearchOrderNo("");
        setSearchPlanNo("");
        setSelectedPlanNo([]);
      }
    } catch (error) {
      console.error("Error during F10_Click:", error);
      Swal.fire({
        icon: "error",
        title: "An error occurred.",
        text: "Unable to delete data. Please contact administrator.",
      });
    }
  };

  const resetButtonState = () => {
    setButtonState((prevState) =>
      Object.keys(prevState).reduce((newState, key) => {
        newState[key] = false;
        return newState;
      }, {})
    );
  };

  const handleF11Click = async () => {
    try {
      // ยืนยันว่าต้องการป้อนข้อมูลต่อไปหรือไม่
      const result = await Swal.fire({
        title: "Confirm",
        html: "Would you like to make the next input?<br>ป้อนข้อมูลต่อไปหรือไม่ ?<br>次入力しますか?",
        icon: "question",
        showCancelButton: true,
        confirmButtonText: "Yes",
        cancelButtonText: "No",
      });

      if (result.isConfirmed) {
        const confirmResult = await Swal.fire({
          title: "Reconfirm",
          html: "Editing contents will be cancelled!<br>Really, are you sure?<br>เนื้อหาที่ทําการแก้ไขจะถูกยกเลิก! แน่ใจจริงๆแล้ว หรือไม่?<br>編集中の内容が取消されます!<br>本当に宜しいで",
          icon: "warning",
          showCancelButton: true,
          confirmButtonText: "Yes",
          cancelButtonText: "No",
        });

        if (!confirmResult.isConfirmed) {
          return;
        }

        // รีเซ็ตค่า state ทั้งหมด
        setSelectedPlanNo([]);
        setOrderData({});
        setSelectedSalesPerson(null);
        setSelectedCustomerAbb(null);
        setCoatingName(null);
        setDeliveryName(null);
        setPriceName(null);
        setUnitName(null);
        setTargetName(null);
        setProgressName(null);
        setPerson_Name(null);
        setupdPerson_Name(null);
        setSchedule_Name(null);
        setStagnat_Scale(null);
        setManHour_Scale(null);
        setPlanData([]); // ถ้า planData เป็นอาร์เรย์
        setSearchOrderNo("");
        setSearchPlanNo("");
        setSelectedMates("");
        setOdProgressName("");
        setIsPlanDateEditable(false);
        setIsResultDateEditable(false);
        setIsDatePickerDisabled(true);
        setActiveRowIndex(null);

        // รีเซ็ตค่าของวันที่ทั้งหมด
        setDates({
          startDate: null,
          endDate: null,
          planDeliDate: null,
          planRegDate: null,
          ptCompDate: null,
          qcCompDate: null,
          planUpdDate: null,
        });

        // รีเซ็ตสถานะของปุ่ม
        setButtonState((prevState) => {
          const newState = {};
          for (const key in prevState) {
            newState[key] = false;
          }
          return newState;
        });

        editPermission(false);
        searchPermission(true);

        localStorage.removeItem("isF6Clicked");
        localStorage.removeItem("isF7Clicked");
      }
    } catch (error) {
      console.error("Error in handleF11Click:", error);
      Swal.fire({
        title: "An error occurred",
        text: "Please try again.",
        icon: "error",
        confirmButtonText: "OK",
      });
    }
  };

  const handleF12Click = () => {
    Swal.fire({
      title: "Are you sure?",
      text: "You are about to reset the form data!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes, reset it!",
      cancelButtonText: "No, cancel",
      reverseButtons: true,
    }).then((result) => {
      if (result.isConfirmed) {
        setSearchOrderNo("");
        setOrderData({});
        // console.log("Form reset to initial state.");

        localStorage.removeItem("isF6Clicked");
        localStorage.removeItem("isF7Clicked");
        Swal.fire("Reset!", "The form data has been reset.", "success");
        navigate("/dashboard");
      } else {
        // console.log("Reset action cancelled.");
      }
    });
  };

  const handlePathClick = (path) => {
    try {
      // 1. Replace all backslashes with forward slashes
      let correctedPath = path.replace(/\\/g, "/");

      // 2. Ensure the protocol has a double slash after the colon
      correctedPath = correctedPath.replace("http:/", "http://");

      // 3. Check if the file exists before opening it
      fetch(correctedPath, { method: "HEAD" })
        .then((response) => {
          if (response.ok) {
            // File exists, open it in a new window
            console.log("Opening URL:", correctedPath);

            const windowWidth = 1024;
            const windowHeight = 768;
            const left = (screen.width - windowWidth) / 2;
            const top = (screen.height - windowHeight) / 2;

            window.open(
              correctedPath,
              "_blank",
              `width=${windowWidth},height=${windowHeight},top=${top},left=${left},status=yes,toolbar=yes,menubar=yes,location=yes,resizable=yes,scrollbars=yes`
            );
          } else {
            // File doesn't exist, show error popup
            console.error("File not found:", correctedPath);
            Swal.fire({
              icon: "error",
              title: "File Not Found",
              text: "The requested PDF file was not found or is inaccessible.",
            });
          }
        })
        .catch((error) => {
          console.error("Error checking file:", error);
          Swal.fire({
            icon: "error",
            title: "Error Occurred",
            text: "An error occurred while verifying the file.",
          });
        });
    } catch (error) {
      console.error("Error processing path:", error);
      Swal.fire({
        icon: "error",
        title: "Error Occurred",
        text: "An error occurred while processing the file path.",
      });
    }
  };

  const PP_View_Click = async () => {
    try {
      const newTab = window.open();
      const url = `/reports/pp-view?searchOrderNo=${searchOrderNo}&searchPlanNo=${searchPlanNo}`;
      newTab.location.href = url;
    } catch (error) {
      console.error("Error in PP_View_Click", error);
      await showSwalError();
    }
  };

  const PD_View_Click = async () => {
    try {
      const newTab = window.open();
      const url = `/reports/pd-view?searchOrderNo=${searchOrderNo}&searchPlanNo=${searchPlanNo}`;
      newTab.location.href = url;
    } catch (error) {
      console.error("Error in PD_View_Click", error);
      await showSwalError();
    }
  };

  const RD_View_Click = async () => {
    try {
      const newTab = window.open();
      const url = `/reports/rd-view?searchOrderNo=${searchOrderNo}&searchPlanNo=${searchPlanNo}`;
      newTab.location.href = url;
    } catch (error) {
      console.error("Error in RD_View_Click", error);
      await showSwalError();
    }
  };

  const swalMessages = {
    confirmTitle: "Confirm",
    confirmText: "Equality Execute!?",
    errorTitle: "An error occurred.",
    errorText: "Please try again.",
    okButton: "Ok",
    cancelCalc: "Do you cancel Calc?",
    changeDateTitle: "Change process plan date",
    changeDateLabel: "Please enter the new date:",
    datePlaceholder: "Enter date (dd/mm/yyyy)",
  };

  const showSwalConfirm = async (text) => {
    return await Swal.fire({
      title: swalMessages.confirmTitle,
      text,
      icon: "info",
      showCancelButton: true,
      confirmButtonText: swalMessages.okButton,
    });
  };

  const showSwalInput = async (defaultValue) => {
    const date = new Date(defaultValue);
    const day = String(date.getUTCDate()).padStart(2, "0");
    const month = String(date.getUTCMonth() + 1).padStart(2, "0");
    const year = date.getUTCFullYear();
    const hours = String(date.getUTCHours()).padStart(2, "0");
    const minutes = String(date.getUTCMinutes()).padStart(2, "0");

    const formattedTime = `${year}/${month}/${day} ${hours}:${minutes}`;

    return await Swal.fire({
      title: swalMessages.changeDateTitle,
      input: "text",
      inputLabel: swalMessages.changeDateLabel,
      inputValue: formattedTime,
      inputPlaceholder: swalMessages.datePlaceholder,
      showCancelButton: true,
    });
  };

  const showSwalError = async () => {
    await Swal.fire({
      title: swalMessages.errorTitle,
      text: swalMessages.errorText,
      icon: "error",
      confirmButtonText: "ตกลง",
    });
  };

  const GRAPH_Click = async () => {
    try {
      const updatedStatusData = {
        Sort: "Plan_Date",
        Print_Object: null,
        Info_View: true,
        Mark_Days: null,
        Color_Separate: true,
        Tg_St_Pl_Date: planData?.Tg_St_Pl_Date,
        Tg_Ed_Pl_Date: planData?.Tg_Ed_Pl_Date
          ? planData.Tg_Ed_Pl_Date
          : new Date(new Date().setDate(new Date().getDate() + 10)),
        TG_ProcessG: planData?.Tg_ProcessG,
        List: false,
        Graph: true,
        Settles: false,
        Settles_Day: null,
      };

      const handleNoData = () => {
        Swal.fire({
          title: "Date check",
          text: "No data!",
          icon: "warning",
        });
      };

      const checkAndProceed = async () => {
        const checkData = await QR_ProG_Plan(updatedStatusData);
        if (checkData && checkData.length > 0) {
          const newTab = window.open();
          const queryString = encodeURIComponent(
            JSON.stringify(updatedStatusData)
          );
          const url = `/reports/RD_ProG_Graph?data=${queryString}`;
          newTab.location.href = url;
        } else {
          handleNoData();
        }
      };

      if (!planData?.Tg_ProcessG) {
        const confirmBackup = await showSwalConfirm(
          "Process Group is none!\nAll group view?"
        );
        if (confirmBackup.isConfirmed) {
          await checkAndProceed();
        }
      } else {
        await checkAndProceed();
      }
    } catch (error) {
      console.error("Error in GRAPH_Click:", error);
      await showSwalError();
    }
  };

  const List_Click = async () => {
    try {
      const updatedStatusData = {
        Sort: "Plan_Date",
        Print_Object: null,
        Info_View: true,
        Mark_Days: null,
        Color_Separate: true,
        Tg_St_Pl_Date: planData?.Tg_St_Pl_Date,
        Tg_Ed_Pl_Date: planData?.Tg_Ed_Pl_Date
          ? planData.Tg_Ed_Pl_Date
          : new Date(new Date().setDate(new Date().getDate() + 10)),
        TG_ProcessG: planData?.Tg_ProcessG,
        List: false,
        Graph: true,
        Settles: false,
        Settles_Day: null,
      };

      const handleNoData = () => {
        Swal.fire({
          title: "Date check",
          text: "No data!",
          icon: "warning",
        });
      };

      const checkAndProceed = async () => {
        const checkData = await QR_ProG_Plan(updatedStatusData);
        if (checkData && checkData.length > 0) {
          const combinedData = {
            ...updatedStatusData,
          };

          const newTab = window.open();
          const queryString = encodeURIComponent(JSON.stringify(combinedData));
          const url = `/reports/RD_ProG_Plan?data=${queryString}`;
          newTab.location.href = url;
        } else {
          handleNoData();
        }
      };

      if (!planData?.Tg_ProcessG) {
        const confirmBackup = await showSwalConfirm(
          "Process Group is none!\nAll group view?"
        );
        if (confirmBackup.isConfirmed) {
          await checkAndProceed();
        }
      } else {
        await checkAndProceed();
      }
    } catch (error) {
      console.error("Error in List_Click:", error);
      await showSwalError();
    }
  };

  const Schedule_BK_Click = async () => {
    try {
      const confirmBackup = await showSwalConfirm(
        "Do you backup Now Schedule?"
      );
      if (confirmBackup.isConfirmed) {
        await Schedule_Bk();
      }
    } catch (error) {
      console.error("Error in Schedule_BK_Click:", error);
      await showSwalError();
    }
  };

  const Schedule_RE_Click = async () => {
    try {
      const confirmBackup = await showSwalConfirm("Do you reset Schedule?");
      if (confirmBackup.isConfirmed) {
        await Schedule_Re();
      }
    } catch (error) {
      console.error("Error in Schedule_RE_Click:", error);
      await showSwalError();
    }
  };

  const Quote_Info_View_Click = async () => {
    try {
      await Quote_Info_View(planData?.Pl_Quote_OdPt_No);
    } catch (error) {
      console.error("Error in Quote_Info_View_Click:", error);
      await showSwalError();
    }
  };

  const Execute_Click = async () => {
    try {
      const result = await Execute(planData, selectedId);
      if (result.status === "warning") {
        Swal.fire({
          title: "Warning",
          text: result.message,
          icon: "warning",
          confirmButtonText: "OK",
        });
        return;
      }
      if (result.status === "question") {
        const userConfirmed = await Swal.fire({
          title: "Confirms",
          text: result.message,
          icon: "question",
          showCancelButton: true,
          confirmButtonColor: "#3085d6",
          cancelButtonColor: "#d33",
          confirmButtonText: "Yes",
          cancelButtonText: "Cancle",
        });
        if (userConfirmed.isConfirmed) {
          await ConfirmExecute(planData, selectedId);
          setLine_Edit_View(false);
          setPlanData((prevData) => ({
            ...prevData,
            Insert_Pr_No: planData?.Target_Pr_No,
            Delete_Pr_No: planData?.Target_Pr_No,
            Mv_Cut_Pr_No: planData?.Target_Pr_No,
            Cp_Copy_Pr_No: planData?.Target_Pr_No,
            Cp_Paste_Pr_No: "",
            Mv_Paste_Pr_No: "",
            Edit_CAT1: true,
            Edit_CAT2: false,
            Edit_CAT3: false,
            Edit_CAT4: false,
          }));
        } else {
          return;
        }
      }
    } catch (error) {
      console.error("Error in Execute_Click:", error);
      await showSwalError();
    }
  };

  const quoteAllClick = async (n) => {
    try {
      let newPlanData = { ...planData };

      if (n === "all") {
        // คำนวณค่า PMT และ PPT ทั้งหมด
        for (let i = 1; i <= 36; i++) {
          let qrmtRaw = planData[`QRMT${i}`];
          let qrptRaw = planData[`QRPT${i}`];

          if (qrmtRaw !== undefined && qrmtRaw !== null && qrmtRaw !== "") {
            let qrmtValue = parseFloat(qrmtRaw);
            newPlanData[`PMT${i}`] = Number.isInteger(qrmtValue)
              ? qrmtValue
              : Math.floor(qrmtValue) + 1;
          }

          if (qrptRaw !== undefined && qrptRaw !== null && qrptRaw !== "") {
            let qrptValue = parseFloat(qrptRaw);
            newPlanData[`PPT${i}`] = Number.isInteger(qrptValue)
              ? qrptValue
              : Math.floor(qrptValue) + 1;
          }
        }
      } else {
        let qrmtRaw = planData[`QRMT${n}`];
        let qrptRaw = planData[`QRPT${n}`];

        // ตรวจสอบว่ามีค่าหรือไม่
        if (qrmtRaw !== undefined && qrmtRaw !== null && qrmtRaw !== "") {
          let qrmtValue = parseFloat(qrmtRaw);
          newPlanData[`PMT${n}`] = Number.isInteger(qrmtValue)
            ? qrmtValue
            : Math.floor(qrmtValue) + 1;
        }

        if (qrptRaw !== undefined && qrptRaw !== null && qrptRaw !== "") {
          let qrptValue = parseFloat(qrptRaw);
          newPlanData[`PPT${n}`] = Number.isInteger(qrptValue)
            ? qrptValue
            : Math.floor(qrptValue) + 1;
        }
      }

      setPlanData(newPlanData); // อัปเดต state
    } catch (error) {
      console.error("Error in quoteAllClick:", error);
      await showSwalError();
    }
  };

  const Schedule_Calc_Click = async () => {
    try {
      const Schedule = await showSwalConfirm(swalMessages.confirmText);
      if (!Schedule.isConfirmed) return;

      // ถามว่าต้องการ Backup หรือไม่
      const confirmBackup = await showSwalConfirm("Now Schedule backup?");
      if (confirmBackup.isConfirmed) {
        await Schedule_Bk();
      }

      const tsSet = await axios.get(`${apiUrl_4000}/plan/schedule-tsSet`);

      async function checkHoliday(date) {
        try {
          const response = await axios.post(
            `${apiUrl_4000}/plan/schedule-checkHoliday`,
            { date: date }
          );

          return response.data.isHoliday;
        } catch (error) {
          console.error("Error checking holiday:", error);
          return false;
        }
      }

      async function countHolidays(startDate, endDate) {
        // แปลงเวลาให้เป็น 00:00:00 ของวันนั้น
        const start = new Date(startDate);
        start.setHours(0, 0, 0, 0);

        const end = new Date(endDate);
        end.setHours(0, 0, 0, 0);

        const res = await axios.post(
          `${apiUrl_4000}/plan/schedule-count-holidays`,
          {
            startDate: start.toISOString(), // ส่งแบบ ISO หรือ YYYY-MM-DD ก็ได้
            endDate: end.toISOString(),
          }
        );

        return res.data.count;
      }

      async function schedule2(Order_No, Parts_No, N) {
        try {
          const response = await axios.post(`${apiUrl_4000}/plan/schedule`, {
            Order_No: Order_No,
            Parts_No: Parts_No,
            N: N,
          });

          console.log("schedule:", response.data.data);
          return response.data.data;
        } catch (error) {
          console.error("Error schedule:", error);
          return false;
        }
      }

      function MsgBox(message, type, title) {
        console.log(`${title}: ${message}`);
      }

      if (planData.Sc_Make_Type === "Equality") {
        const maxLines = 36;
        let No = 0;
        let N = 1;
        let Fix_N = 0;
        let St_Rev = 0;
        let Sc_Scale = 1;

        const cfg = tsSet.data.data;
        const St_Time = cfg.Sc_Stagnat_Time;
        const St_Scale = cfg.Sc_Stagnat_Scale;
        const P_Scale = cfg.Sc_Person_Scale;
        const M_Scale = cfg.Sc_Machine_Scale;
        const O_Scale = cfg.Sc_Outside_Scale;
        const MH_Scale = cfg.Sc_ManHour_Scale;

        // สร้างอาเรย์สำหรับเก็บค่าต่างๆ
        const PSL = Array(maxLines + 1).fill(0);
        const Fix_Pr_N = Array(maxLines + 1).fill(0);
        const Fix_Re_MH = Array(maxLines + 1).fill(0);
        const Fix_Date_No = Array(maxLines + 1).fill(0);
        const Fix_Date = Array(maxLines + 1).fill(null);

        // STEP B: คำนวณ PSL, ตั้งค่า default PPD และเก็บจุด Fix
        while (No < Number(planData.End_No)) {
          No++;
          const rpd = planData[`RPD${No}`];
          const ppd = planData[`PPD${No}`];

          if (rpd != null) {
            // ถ้ามี RPD แต่ไม่มี PPD ให้ copy
            if (!ppd) {
              planData[`PPD${No}`] = rpd;
            }
            PSL[No] = 0;
          } else {
            // คำนวณ PSL ตาม P_Type
            const typ = planData[`P_Type${No}`];
            if (typ === "M") {
              PSL[No] =
                St_Time * St_Scale + planData[`PPL${No}`] * P_Scale * MH_Scale;
            } else if (typ === "A") {
              PSL[No] =
                St_Time * St_Scale +
                planData[`PPL${No}`] * P_Scale * MH_Scale +
                planData[`PML${No}`] * M_Scale * MH_Scale;
            } else if (typ === "O") {
              PSL[No] = St_Time * St_Scale + planData[`PPL${No}`] * O_Scale;
            }

            // บวก 720 ถ้า prev Type=O หรือ prev S_Type=F
            if (
              No > 1 &&
              (planData[`P_Type${No - 1}`] === "O" ||
                planData[`S_Type${No - 1}`] === "F")
            ) {
              PSL[No] += 720;
            }
            // บวก 720 อีกครั้งถ้าเป็นบรรทัดสุดท้าย
            if (No === Number(planData.End_No)) {
              PSL[No] += 720;
            }

            // เก็บสถิติสำหรับ segment ปัจจุบัน
            Fix_Pr_N[N] += 1;
            Fix_Re_MH[N] += PSL[No];

            // จุด fix เมื่อ S_Type='F' และมี PPD
            if (planData[`S_Type${No}`] === "F" && planData[`PPD${No}`]) {
              const dt = new Date(planData[`PPD${No}`]);
              const isoDate = dt.toISOString().slice(0, 10);
              if (await checkHoliday(isoDate)) {
                Swal.fire({
                  title: "Error",
                  text: `Line ${No}: PPD falls on holiday`,
                  icon: "error",
                });
                return;
              }
              Fix_Date_No[N] = No;
              Fix_Date[N] = isoDate;
              Fix_N++;
              N++;
            }

            // จัดการบรรทัดสุดท้าย (Pt_Delivery - Pl_Ed_Rev_Day)
            if (No === Number(planData.End_No)) {
              const ptD = new Date(planData.Pt_Delivery);
              ptD.setDate(ptD.getDate() - Number(planData.Pl_Ed_Rev_Day));
              const ymd = ptD.toISOString().split("T")[0];
              if (await checkHoliday(ymd)) {
                Swal.fire({
                  title: "Error",
                  text: "Pt_Delivery-RevDay falls on holiday",
                  icon: "error",
                });
                return;
              }
              planData[`PPD${No}`] = ymd;
              Fix_Date_No[N] = No;
              Fix_Date[N] = ymd;
              Fix_N++;
              N++;
              for (let i = 1; i <= 36; i++) {
                if (
                  new Date(planData[`PPD${i}`]) > new Date(planData[`PPD${No}`])
                ) {
                  Swal.fire({
                    title: "Error",
                    text: `The value in row ${i} exceeds Pt_Delivery. Please update the date in row ${i} accordingly.`,
                    icon: "error",
                  });
                  return;
                }
              }
            }
          }
        }

        // STEP C: ตั้งค่า Now_No, Now_Date, Final_No, Final_Date
        let Now_No = Number(planData.Now_No) || 1;
        let Final_No = Now_No - 1;
        let Now_Date = new Date();
        Now_Date.setDate(Now_Date.getDate() + Number(planData.Pl_Ed_Rev_Day));
        let Final_Date = new Date(Now_Date);

        // ตรวจ special case สำหรับบรรทัดแรก
        N = 0;
        if (Now_No === 1 && planData.S_Type1 === "F" && planData.PPD1) {
          const pd1 = new Date(planData.PPD1);
          if (pd1 > Date.now() - 86400000) {
            Final_No = 1;
            Now_No = 2;
            Now_Date = pd1;
            Final_Date = new Date(pd1);
            N = 1;
          }
        }

        outer: while (N < Fix_N) {
          N++;
          // STEP1: คำนวณวันทำงานที่เหลือ ระหว่าง Now_Date → Fix_Date[N]
          const hcount = await countHolidays(
            Now_Date.toISOString().slice(0, 10),
            Fix_Date[N]
          );
          const ReDays = (new Date(Fix_Date[N]) - Now_Date) / 86400000 - hcount;
          const MinDays = Fix_Re_MH[N] / 1440;

          let No2 = Now_No - 1;
          let TempDate = new Date(Now_Date);

          inner: while (No2 < Fix_Date_No[N]) {
            No2++;

            // 1) กรณีผู้ใช้กำหนด PPD เอง (Override) → ถ้าเกิน Fix_Date[N] ให้ปรับเป็นวันที่ฟิคทันที

            // 2) กรณีคำนวณเอง (No2 < Fix_Date_No[N])
            if (No2 < Fix_Date_No[N]) {
              // คำนวณ TempDate ตามสูตรเดิม
              TempDate = new Date(
                TempDate.getTime() +
                  ((PSL[No2] / 1440) * Sc_Scale + St_Rev) * 86400000
              );

              // ข้ามวันหยุด
              while (await checkHoliday(TempDate.toISOString().split("T")[0])) {
                TempDate.setDate(TempDate.getDate() + 1);
                TempDate.setUTCHours(0, 0, 0, 0); // หรือ setHours(0, 0, 0, 0); ถ้าใช้ local time
              }

              // ตรวจสอบว่า TempDate ไม่เกิน Fix_Date[N]
              const fixDt = new Date(Fix_Date[N]);
              if (TempDate > fixDt) {
                // ถ้าเกิน ให้ปรับเป็น Fix_Date[N] ทันที
                TempDate = fixDt;
              }

              // บันทึก PPD เลขนี้ (มั่นใจว่าไม่เกิน Fix_Date[N])
              planData[`PPD${No2}`] = TempDate.toISOString().slice(0, 10);
              Now_No = No2 + 1;
              Now_Date = new Date(TempDate);
              continue inner;
            } else {
              // 3) กรณี No2 == Fix_Date_No[N] → จับคู่กับวันที่ฟิค
              const fixDt = new Date(Fix_Date[N]);

              if (TempDate > fixDt) {
                // ถ้า TempDate เกิน ให้ปรับเป็น Fix_Date[N] ทันที
                TempDate = fixDt;
                planData[`PPD${No2}`] = TempDate.toISOString().slice(0, 10);

                // รีเซ็ตค่า Sc_Scale, St_Rev ตามเงื่อนไขเดิม
                const newFix = new Date(Fix_Date[N]);
                if (newFix.getTime() === TempDate.getTime()) {
                  Sc_Scale = 1;
                  St_Rev = 0;
                } else if (newFix < TempDate) {
                  const h2 = await countHolidays(
                    Final_Date.toISOString().slice(0, 10),
                    newFix.toISOString().slice(0, 10)
                  );
                  const rd2 = (newFix - Final_Date) / 86400000 - h2;
                  Sc_Scale = rd2 / (Fix_Re_MH[N] / 1440);
                  St_Rev = 0;
                } else {
                  const h2 = await countHolidays(
                    Final_Date.toISOString().slice(0, 10),
                    newFix.toISOString().slice(0, 10)
                  );
                  const rd2 = (newFix - Final_Date) / 86400000 - h2;
                  Sc_Scale = 1;
                  St_Rev = (rd2 - MinDays) / Fix_Pr_N[N];
                }

                Now_No = No2 + 1;
                Now_Date = new Date(TempDate);
                continue outer;
              }

              // กรณี TempDate ≤ fixDt
              if (TempDate.getTime() !== fixDt.getTime()) {
                // ถ้าไม่เท่ากัน แต่ไม่เกิน ให้บันทึกเป็น fixDt ทันที
                planData[`PPD${No2}`] = fixDt.toISOString().slice(0, 10);
                Final_No = No2;
                Final_Date = new Date(fixDt);
                Now_No = No2 + 1;
                Now_Date = new Date(fixDt);
                Sc_Scale = 1;
                St_Rev = 0;
                continue outer;
              }

              // กรณีตรงเป๊ะ (TempDate === fixDt)
              Now_No = No2 + 1;
              Final_No = No2;
              Now_Date = new Date(TempDate);
              Final_Date = new Date(TempDate);
              Sc_Scale = 1;
              St_Rev = 0;
            }
          }
        }

        // สรุปผล PPD ทั้งหมด
        const ppdResult = {};
        for (let i = 1; i <= 36; i++) {
          const key = `PPD${i}`;
          if (planData[key]) {
            ppdResult[key] = planData[key];
          }
          setPlanData((prevData) => ({
            ...prevData,
            [`PPD${i}`]: ppdResult[key],
          }));
        }

        // อัพเดตสถานะ Order ถ้ายังเป็น 0
        if (orderData?.Od_Progress_CD === "0") {
          setOrderData((prevData) => ({
            ...prevData,
            Od_Progress_CD: "1",
          }));
          await updateOrderpl({
            Order_No: orderData.Order_No,
            Od_Progress_CD: "1",
          });
        }
        setIsSave(true);
      }
    } catch (error) {
      console.error("Error in Schedule_Calc_Click:", error);
      await showSwalError();
    }
  };

  // const Schedule_Calc_Click = async () => {
  //   try {
  //     let isValid = false;

  //     // ยืนยันการ Execute ก่อน
  //     const Schedule = await showSwalConfirm(swalMessages.confirmText);
  //     if (!Schedule.isConfirmed) return;

  //     // ถามว่าต้องการ Backup หรือไม่
  //     const confirmBackup = await showSwalConfirm("Now Schedule backup?");
  //     if (confirmBackup.isConfirmed) {
  //       await Schedule_Bk();
  //     }

  //     // เรียกใช้ฟังก์ชัน Schedule_Calc
  //     const result = await Schedule_Calc();
  //     const PPDValues = result.data;
  //     if (result.status === "warning") {
  //       Swal.fire({
  //         title: "Confirms",
  //         text: `${result.message1}\n${result.message2}`,
  //         icon: "warning",
  //       });
  //       return;
  //     }

  //     if (result.status === "need_corrections") {
  //       const { corrections } = result; // สมมุติ backend ส่งมาหลาย step

  //       const updatedDates = {}; // เก็บวันที่ที่ผู้ใช้กรอกใหม่

  //       for (const correction of corrections) {
  //         const { stepNo, ppc, suggested } = correction;

  //         // แสดง SweetAlert ให้กรอกวันที่ใหม่
  //         const userInput = await showSwalInput(
  //           suggested // ใส่เป็นค่า default ถ้ามี
  //         );

  //         // ถ้าผู้ใช้กดยกเลิก
  //         if (!userInput.isConfirmed) {
  //           Swal.fire("ยกเลิกแล้ว", "", "info");
  //           return;
  //         }

  //         // แปลงวันที่ที่กรอกให้เป็น ISO string โดยไม่เพี้ยน timezone
  //         const parsedDate = new Date(userInput.value);
  //         const newDateValue = new Date(
  //           parsedDate.getTime() - parsedDate.getTimezoneOffset() * 60000
  //         ).toISOString();

  //         // เก็บลง object
  //         updatedDates[`PPD${stepNo}`] = newDateValue;
  //         setPlanData((prevData) => ({
  //           ...prevData,
  //           [`PPD${stepNo}`]: newDateValue,
  //         }));
  //         console.log(updatedDates);
  //       }

  //       // ส่งวันที่ใหม่ทั้งหมดกลับไปที่ backend ทีเดียว
  //       const retryResult = await update_temdate(updatedDates);

  //       return;
  //     }

  //     inputs.forEach((id) => {
  //       const dateValue = PPDValues[`PPD${id}`];
  //       const formattedDate = dateValue ? new Date(dateValue) : null;

  //       setPlanData((prevData) => ({
  //         ...prevData,
  //         [`PPD${id}`]: formattedDate,
  //       }));
  //     });

  //     if (result.status !== "warning") {
  //       if (orderData?.Od_Progress_CD === "0") {
  //         setOrderData((prevData) => ({
  //           ...prevData,
  //           Od_Progress_CD: "1",
  //         }));
  //         await updateOrderpl({
  //           Order_No: orderData.Order_No,
  //           Od_Progress_CD: "1",
  //         });
  //       }

  //       setIsSave(true);

  //       return;
  //     }

  //     if (isValid) {
  //       if (orderData?.Od_Progress_CD === "0") {
  //         setOrderData((prevData) => ({
  //           ...prevData,
  //           Od_Progress_CD: "1",
  //         }));
  //         await updateOrderpl({
  //           Order_No: orderData.Order_No,
  //           Od_Progress_CD: "1",
  //         });
  //       }
  //       setIsSave(true);
  //     }
  //   } catch (error) {
  //     console.error("Error in Schedule_Calc_Click:", error);
  //     await showSwalError();
  //   }
  // };

  useEffect(() => {
    if (!hasUserEditedOrderNo && !searchOrderNo && StatusData?.Obj_Od_No) {
      setSearchOrderNo(StatusData.Obj_Od_No);
    }
    if (!hasUserEditedOrderNo && !searchPlanNo && StatusData?.Obj_Pt_No) {
      setSearchPlanNo(StatusData.Obj_Pt_No);
    }
  }, [StatusData, hasUserEditedOrderNo]);

  useEffect(() => {
    if (planData?.Target_Pr_No) {
      setPlanData((prevData) => ({
        ...prevData,
        ...(planData?.Edit_CAT1 && { Insert_Pr_No: planData.Target_Pr_No }),
        ...(planData?.Edit_CAT2 && { Delete_Pr_No: planData.Target_Pr_No }),
        ...(planData?.Edit_CAT3 && { Mv_Cut_Pr_No: planData.Target_Pr_No }),
        ...(planData?.Edit_CAT4 && { Cp_Copy_Pr_No: planData.Target_Pr_No }),
      }));
    }
  }, [
    planData?.Edit_CAT1,
    planData?.Edit_CAT2,
    planData?.Edit_CAT3,
    planData?.Edit_CAT4,
  ]);

  useEffect(() => {
    if (orderData?.Od_Ctl_Person_CD && WorkerData.length > 0) {
      const selectedGroup = WorkerData.find(
        (item) => item.Worker_CD === orderData.Od_Ctl_Person_CD
      );
      setSelectedSalesPerson(selectedGroup ? selectedGroup.Worker_Abb : "");
    }

    if (planData?.Pl_Reg_Person_CD && WorkerData.length > 0) {
      const selectedGroup = WorkerData.find(
        (item) => item.Worker_CD === planData.Pl_Reg_Person_CD
      );
      setPerson_Name(selectedGroup ? selectedGroup.Worker_Abb : "");
    }

    if (planData?.Pl_Upd_Person_CD && WorkerData.length > 0) {
      const selectedGroup = WorkerData.find(
        (item) => item.Worker_CD === planData.Pl_Upd_Person_CD
      );
      setupdPerson_Name(selectedGroup ? selectedGroup.Worker_Abb : "");
    }
    if (planData?.Pt_Unit_CD && UnitsData.length > 0) {
      const selectedGroup = UnitsData.find(
        (item) => item.Unit_CD === planData?.Pt_Unit_CD
      );
      setUnitName(selectedGroup ? selectedGroup.Unit_Abb : "");
    }
  }, [
    orderData?.Od_Ctl_Person_CD,
    planData?.Pl_Reg_Person_CD,
    planData?.Pl_Upd_Person_CD,
    planData?.Pt_Unit_CD,
    UnitsData,
    WorkerData,
  ]);

  useEffect(() => {
    if (orderData?.Specific_CD && SpecificData.length > 0) {
      const selectedGroup = SpecificData.find(
        (item) => item.Specific_CD === orderData?.Specific_CD
      );

      setSpecificName(selectedGroup ? selectedGroup.Specific_Name : "");
    }
  }, [orderData?.Specific_CD, SpecificData]);

  useEffect(() => {
    if (orderData?.Customer_CD && CustomerData.length > 0) {
      const selectedGroup = CustomerData.find(
        (item) => item.Customer_CD === orderData.Customer_CD
      );

      setSelectedCustomerAbb(selectedGroup ? selectedGroup.Customer_Abb : "");
    }
  }, [orderData?.Customer_CD, CustomerData]);

  useEffect(() => {
    if (orderData?.Coating_CD && CoatingData.length > 0) {
      const selectedGroup = CoatingData.find(
        (item) => item.Coating_CD === orderData.Coating_CD
      );

      setCoatingName(selectedGroup ? selectedGroup.Coating_Name : "");
    }
  }, [orderData?.Coating_CD, CoatingData]);

  useEffect(() => {
    if (orderData?.Od_Progress_CD && OdProgressData.length > 0) {
      const selectedGroup = OdProgressData.find(
        (item) => item.Od_Progress_CD === orderData.Od_Progress_CD
      );

      setOdProgressName(selectedGroup ? selectedGroup.Od_Progress_Name : "");
    }
  }, [orderData?.Od_Progress_CD, OdProgressData]);

  useEffect(() => {
    if (orderData?.Delivery_CD && DeliveryData.length > 0) {
      const selectedGroup = DeliveryData.find(
        (item) => item.Delivery_CD === orderData.Delivery_CD
      );

      setDeliveryName(selectedGroup ? selectedGroup.Delivery_Name : "");
    }
  }, [orderData?.Delivery_CD, DeliveryData]);

  useEffect(() => {
    if (orderData?.Price_CD && PriceData.length > 0) {
      const selectedGroup = PriceData.find(
        (item) => item.Price_CD === orderData?.Price_CD
      );

      setPriceName(selectedGroup ? selectedGroup.Price_Name : "");
    }
  }, [orderData?.Price_CD, PriceData]);

  useEffect(() => {
    if (orderData?.Target_CD && TargetData.length > 0) {
      const selectedGroup = TargetData.find(
        (item) => item.Target_CD === orderData.Target_CD
      );

      setTargetName(selectedGroup ? selectedGroup.Target_Name : "");
    }
  }, [orderData?.Target_CD, TargetData]);

  useEffect(() => {
    if (planData?.Pl_Progress_CD && plprogressData.length > 0) {
      const selectedGroup = plprogressData.find(
        (item) => item.Pl_Progress_CD === planData.Pl_Progress_CD
      );

      setProgressName(selectedGroup ? selectedGroup.Pl_Progress_Symbol : "");
    }
  }, [planData?.Pl_Progress_CD, plprogressData]);

  useEffect(() => {
    if (planData?.Pl_Schedule_CD && ScheduleData.length > 0) {
      const selectedGroup = ScheduleData.find(
        (item) => item.Schedule_CD === planData.Pl_Schedule_CD
      );

      setSchedule_Name(selectedGroup ? selectedGroup.Schedule_Symbol : "");
      setStagnat_Scale(selectedGroup ? selectedGroup.Stagnat_Scale : "");
      setManHour_Scale(
        selectedGroup ? Math.round(selectedGroup.ManHour_Scale) : ""
      );
    }
  }, [planData?.Pl_Schedule_CD, ScheduleData]);

  useEffect(() => {
    if (Search_Odpt_No) {
      selectPartsData(searchOrderNo, searchPlanNo);
    }
  }, [Search_Odpt_No]);

  useEffect(() => {
    if (planData?.Connect_Pt_No) {
      hasConnectData(planData?.Connect_Od_No, planData?.Connect_Pt_No);
    }
  }, [planData?.Connect_Pt_No]);

  useEffect(() => {
    if (!planData?.Sc_Make_Type) {
      setPlanData((prev) => ({ ...prev, Sc_Make_Type: "Equality" }));
    }
  }, [planData]);

  // ฟังก์ชันเพื่อค้นหาค่า Pt_Delivery ตาม Parts_No
  const getPtDeliveryByPartsNo = (partsNo) => {
    const foundItem = selectedPlanNo?.find?.(
      (item) => item.Parts_No === partsNo
    );
    return foundItem ? new Date(foundItem.Pt_Delivery) : null;
  };

  useEffect(() => {
    const ptDelivery = getPtDeliveryByPartsNo(searchPlanNo);
    if (ptDelivery) {
      setDates((prevDates) => ({
        ...prevDates,
        planDeliDate: planData?.Pt_Delivery,
      }));
    }
  }, [planData?.Pt_Delivery]);

  useEffect(() => {
    const fetchData = async () => {
      const amount = await calculateAmount();
      setPlanData((prevData) => ({
        ...prevData,
        Amount: amount,
      }));
    };

    fetchData();
  }, [
    planData?.Money_Object,
    planData?.Pt_Qty,
    planData?.Pt_Spare_Qty,
    planData?.Pt_NG_Qty,
    orderData,
  ]);

  const handleDoubleClick = (id) => {
    if (!buttonState.F2) {
      setSelectedId(id);
      setLine_Edit_View(true);
      setPlanData((prevData) => ({
        ...prevData,
        Target_Pr_No: id,
        Insert_Pr_No: id,
        Delete_Pr_No: id,
        Mv_Cut_Pr_No: id,
        Cp_Copy_Pr_No: id,
        Cp_Paste_Pr_No: "",
        Mv_Paste_Pr_No: "",
        Edit_CAT1: true,
        Edit_CAT2: false,
        Edit_CAT3: false,
        Edit_CAT4: false,
      }));
    }
  };

  const handleClosePopup = () => {
    setLine_Edit_View(false);
  };

  const getBackgroundClass = (color) => {
    switch (color) {
      case 255:
        return "bg-red-500"; // สีแดง
      case 65535:
        return "bg-yellow-500"; // สีเหลือง
      case 12632256:
        return "bg-gray-400"; // สีเทา
      default:
        return "bg-gray-200"; // สีเริ่มต้น
    }
  };

  const [activeRowIndex, setActiveRowIndex] = useState(null);

  const PartsKey = ConnectData?.Parts_CD;
  const foundPart = (PartsData || []).find(
    (Parts) => Parts.Parts_CD === PartsKey
  );
  const Connect_Pt_No = planData?.Connect_Pt_No ?? "";
  const Connect_Pt_Abb = Connect_Pt_No === "" ? "" : foundPart?.Parts_Abb ?? "";

  const processKey = ConnectData?.[`PPC${planData?.Connect_Pr_No}`];
  const Connect_Pr_Abb = (ProcessCData || [])
    .filter((Process) => Process.Process_CD === processKey)
    .map((Process) => Process.Process_Abb);

  const quotleKey = planData?.Pl_Quote_CD;
  const Pl_Quote_Abb = (QuotleData || [])
    .filter((quotle) => quotle.Pl_Quote_CD === quotleKey)
    .map((quotle) => quotle.Pl_Quote_Abb);

  const rows = inputs.map((id) => {
    const processKey = planData?.[`PPC${id}`];
    const ProcessNamesForRow = (ProcessCData || [])
      .filter((Process) => Process.Process_CD === processKey)
      .map((Process) => Process.Process_Abb);

    const process2Key = planData?.[`QPPC${id}`];
    const ProcessNames2ForRow = (ProcessCData || [])
      .filter((Process) => Process.Process_CD === process2Key)
      .map((Process) => Process.Process_Abb);

    return {
      mp: (
        <div className="flex flex-col space-y-2 w-full">
          <div className="flex space-x-2 w-full">
            <select
              disabled
              id={`QPPC${id}`}
              value={planData?.[`QPPC${id}`] || ""}
              onChange={handlePlanInputChange}
              className={`border rounded px-2 py-1 text-xs w-full h-6 ${getBackgroundClass(
                planData?.[`QPPC${id}_bg`]
              )}`}
            >
              <option value={planData?.[`QPPC${id}`] || ""}>
                {ProcessNames2ForRow}
              </option>
            </select>
          </div>
          <div className="flex space-x-2 items-center w-full">
            <input
              disabled
              id={`QRMT${id}`}
              type="text"
              value={
                planData?.[`QRMT${id}`] !== undefined
                  ? planData[`QRMT${id}`]
                  : ""
              }
              onChange={handlePlanInputChange}
              className={`border rounded px-2 py-1 text-xs w-16 ${getBackgroundClass(
                planData?.[`QRMT${id}_bg`]
              )}`}
            />
            <input
              disabled
              id={`QRPT${id}`}
              type="text"
              value={
                planData?.[`QRPT${id}`] !== undefined
                  ? Number.isInteger(planData[`QRPT${id}`])
                    ? planData[`QRPT${id}`] // แสดงเป็นจำนวนเต็ม
                    : planData[`QRPT${id}`].toFixed(2) // แสดงเป็นทศนิยม 2 ตำแหน่ง
                  : ""
              }
              onChange={handlePlanInputChange}
              className={`border rounded px-2 py-1 text-xs w-16 ${getBackgroundClass(
                planData?.[`QRPT${id}_bg`]
              )}`}
            />
          </div>
        </div>
      ),
      plan_process: (
        <div>
          {/* ดับเบิ้ลคลิกที่ <div> เพื่อแสดง Popup */}
          <div onDoubleClick={() => handleDoubleClick(id)}>
            <Select
              inputId={`PPC${id}`}
              value={{
                label: ProcessNamesForRow,
                value: planData?.[`PPC${id}`] || "",
              }}
              onChange={(selectedOption) =>
                handlePlanInputChange(null, selectedOption, `PPC${id}`)
              }
              options={qmprocessData?.map((item) => ({
                label: item.Process_Abb,
                value: item.Process_CD,
              }))}
              className="text-xs text-left w-[90px]"
              tabIndex={300 + id}
              noOptionsMessage={() => "No data found"}
              isDisabled={!isPlanDateEditable}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  const next = document.getElementById(`PPC${id + 1}`);
                  if (next) next.focus();
                }
              }}
              styles={{
                control: (base, state) => ({
                  ...base,
                  backgroundColor: state.isDisabled ? "white" : "white",
                  color: state.isDisabled ? "black" : "black",
                  cursor: state.isDisabled ? "not-allowed" : "default",
                }),
                singleValue: (base, state) => ({
                  ...base,
                  color: state.isDisabled ? "black" : "black",
                }),
                option: (base, state) => ({
                  ...base,
                  color: state.isDisabled ? "black" : "black",
                  backgroundColor: state.isFocused ? "#e5e7eb" : "white",
                }),
              }}
            />
          </div>
        </div>
      ),
      min: (
        <div>
          <input
            disabled
            id={`PMT${id}`}
            type="text"
            value={
              planData?.[`PMT${id}`] !== undefined &&
              planData?.[`PMT${id}`] !== null
                ? planData?.[`PMT${id}`]
                : ""
            }
            onChange={handlePlanInputChange}
            className="border rounded px-2 py-1 w-[70px] text-right text-xs bg-white"
            tabIndex={400 + id}
          />
        </div>
      ),
      mind: (
        <div>
          <input
            disabled
            id={`PPT${id}`}
            type="text"
            value={
              planData?.[`PPT${id}`] !== undefined &&
              planData?.[`PPT${id}`] !== null
                ? planData?.[`PPT${id}`]
                : ""
            }
            onChange={handlePlanInputChange}
            className="border rounded px-2 py-1 w-[70px] text-right text-xs bg-white"
            tabIndex={500 + id}
          />
        </div>
      ),
      time: (
        <div className="flex flex-row space-x-1">
          <select
            disabled
            id={`T_Type${id}`}
            value={planData?.[`T_Type${id}`] || ""}
            onChange={handlePlanInputChange}
            className="border rounded py-1 text-xs w-12"
            tabIndex={600 + id}
          >
            <option value={planData?.[`T_Type${id}`] || ""}>
              {planData?.[`T_Type${id}`]}
            </option>
            <option value=""></option>
            <option value="P">P</option>
            <option value="L">L</option>
          </select>
          <select
            disabled
            id={`P_Type${id}`}
            value={planData?.[`P_Type${id}`] || ""}
            onChange={handlePlanInputChange}
            className="border rounded py-1 text-xs w-12"
            tabIndex={600 + id}
          >
            <option value={planData?.[`P_Type${id}`] || ""}>
              {planData?.[`P_Type${id}`]}
            </option>
            <option value=""></option>
            <option value="M">M</option>
            <option value="A">A</option>
            <option value="O">O</option>
          </select>
          <select
            disabled
            id={`S_Type${id}`}
            value={planData?.[`S_Type${id}`] || ""}
            onChange={handlePlanInputChange}
            className="border rounded py-1 text-xs w-12"
            tabIndex={600 + id}
          >
            <option value={planData?.[`S_Type${id}`] || ""}>
              {planData?.[`S_Type${id}`]}
            </option>
            <option value=""></option>
            <option value="C">C</option>
            <option value="F">F</option>
          </select>
        </div>
      ),
      plan_date: (
        <div className="relative">
          <DatePicker
            key={`datepicker-PPD${id}`}
            selected={
              planData?.[`PPD${id}`]
                ? new Date(
                    new Date(planData[`PPD${id}`]).getUTCFullYear(),
                    new Date(planData[`PPD${id}`]).getUTCMonth(),
                    new Date(planData[`PPD${id}`]).getUTCDate()
                  )
                : null
            }
            onChange={(date) =>
              handlePlanInputChange(null, null, `PPD${id}`, date)
            }
            dateFormat="dd/MM/yyyy"
            showIcon
            toggleCalendarOnIconClick
            popperPlacement="bottom-start"
            portalId="root-portal"
            inline={false}
            disabled={!isPlanDateEditable}
            tabIndex={700 + id}
            isClearable // <<< ✅ ปุ่มลบค่านี้
            className="border border-gray-300 rounded-md px-1 w-[110px] h-7 cursor-pointer bg-white"
          />
        </div>
      ),
      instructions: (
        <div>
         <textarea
                disabled
                rows={2}
                id={`PPV${id}`}
                value={planData?.[`PPV${id}`] || ""}
                onChange={(e) => handlePPVChange(id, e.target.value)}
                onInput={(e) => {
                  const el = e.target;
                  el.style.height = "auto"; // รีเซ็ตก่อน
                  el.style.height = el.scrollHeight + "px"; // ตั้งตามเนื้อหา
                }}
                className="border rounded px-1 text-sm w-[380px] bg-white overflow-hidden"
                tabIndex={800 + id}
              />

        </div>
      ),
      result_date: (
        <div>
          <DatePicker
            key={`datepicker-RPD${id}`}
            selected={
              planData?.[`RPD${id}`] ? new Date(planData[`RPD${id}`]) : null
            }
            onChange={(date) =>
              handlePlanInputChange(null, null, `RPD${id}`, date)
            }
            dateFormat="dd/MM/yyyy"
            showIcon
            toggleCalendarOnIconClick
            popperPlacement="bottom-start"
            portalId="root-portal"
            inline={false}
            disabled={!isResultDateEditable}
            tabIndex={900 + id}
            className="border border-gray-300 rounded-md px-1 w-[90px] h-7 cursor-pointer bg-white"
          />
        </div>
      ),
      resultmachine: (
        <div>
          <input
            disabled
            id={`RMT${id}`}
            type="text"
            value={
              planData?.[`RMT${id}`] !== undefined &&
              planData?.[`RMT${id}`] !== null
                ? planData?.[`RMT${id}`]
                : ""
            }
            onChange={handlePlanInputChange}
            className="border rounded py-1 px-1 w-[70px] text-xs text-right bg-white"
            tabIndex={1000 + id}
          />
        </div>
      ),
      result_person: (
        <div>
          <input
            disabled
            id={`RPT${id}`}
            type="text"
            value={
              planData?.[`RPT${id}`] !== undefined &&
              planData?.[`RPT${id}`] !== null
                ? planData?.[`RPT${id}`]
                : ""
            }
            onChange={handlePlanInputChange}
            className="border rounded py-1 px-1 w-[70px] text-xs text-right bg-white"
            tabIndex={1100 + id}
          />
        </div>
      ),
      resultqty: (
        <div>
          <input
            disabled
            id={`RPN${id}`}
            type="text"
            value={
              planData?.[`RPN${id}`] !== undefined &&
              planData?.[`RPN${id}`] !== null
                ? planData?.[`RPN${id}`]
                : ""
            }
            onChange={handlePlanInputChange}
            className="border rounded py-1 px-1 w-[70px] text-xs text-right bg-white"
            tabIndex={1200 + id}
          />
        </div>
      ),
      asp: (
        <p className="mb-6 text-sm font-normal text-gray-500 dark:text-gray-400">
          {planData?.[`ASP${id}`] !== undefined &&
          planData?.[`ASP${id}`] !== null
            ? planData?.[`ASP${id}`]
            : ""}
        </p>
      ),
      bk: (
        <input
          disabled
          id={`BKD${id}`}
          type="text"
          value={
            planData?.[`BKD${id}`]
              ? new Date(planData[`BKD${id}`]).toISOString().split("T")[0]
              : ""
          }
          onChange={handlePlanInputChange}
          className="border-none text-center text-gray-500 dark:text-gray-400 w-24"
          tabIndex={1300 + id}
        />
      ),
      pi_machine: (
        <input
          disabled
          id={`PML${id}`}
          type="text"
          value={
            planData?.[`PML${id}`] !== undefined &&
            planData?.[`PML${id}`] !== null
              ? planData?.[`PML${id}`]
              : ""
          }
          onChange={handlePlanInputChange}
          className="border-none text-center  text-gray-500 dark:text-gray-400 w-24"
          tabIndex={1400 + id}
        />
      ),
      pi_person: (
        <input
          disabled
          id={`PPL${id}`}
          type="text"
          value={
            planData?.[`PPL${id}`] !== undefined &&
            planData?.[`PPL${id}`] !== null
              ? planData?.[`PPL${id}`]
              : ""
          }
          onChange={handlePlanInputChange}
          className="border-none text-center  text-gray-500 dark:text-gray-400 text-sm w-24"
          tabIndex={1500 + id}
        />
      ),
    };
  });
  const searchPermission = (status) => {
    document.getElementById("Search_Order_No").disabled = !status;
    document.getElementById("Search_Parts_No").disabled = !status;
  };
  //disable fields depend on status
  const editPermission = (status) => {
    // รายชื่อฟิลด์ที่ไม่ใช่ DatePicker
    const fields = [
      "Parts_No",
      "Pt_Delivery",
      "Pl_Reg_Person_CD",
      "Parts_CD",
      "Pt_Material",
      "Pt_Qty",
      "Pt_Unit_CD",
      "Pt_Split",
      "Pt_Spare_Qty",
      "Pt_NG_Qty",
      "Connect_Od_No",
      "Connect_Pt_No",
      "Connect_Pr_No",
      "Pt_Pending",
      "Outside",
      "Money_Object",
      "Pl_St_Rev_Day",
      "Pl_Ed_Rev_Day",
      "Info1",
      "Info2",
      "Info3",
      "Info4",
      "Info5",
      "Info6",
      "Info_Chk1",
      "Info_Chk2",
      "Info_Chk3",
      "Info_Chk4",
      "Info_Chk5",
      "Info_Chk6",
      "Pt_CAT1",
      "Pt_CAT2",
      "Pt_CAT3",
      "Pl_Progress_CD",
      "Pt_Instructions",
      "Pt_Remark",
      "Pt_Information",
      "Pl_Schedule_CD",
      "Pl_Reg_Date",
      "Pt_Complete_Date",
      "Pt_I_Date",
      "Pl_Upd_Date",
    ];

    // ปรับฟิลด์ที่ไม่ใช่ DatePicker
    fields.forEach((field) => {
      const element = document.getElementById(field);
      if (element) {
        element.disabled = !status;
      }
    });

    // ใช้ ref สำหรับ DatePicker PPD
    if (planDateRef.current) {
      planDateRef.current.disabled = !status;
    }
    // ใช้ ref สำหรับ DatePicker RPD
    if (resultDateRef.current) {
      resultDateRef.current.disabled = !status;
    }

    // ควบคุมฟิลด์ที่มีลำดับ P ควบคู่กับการใช้ document.getElementById
    for (let i = 1; i <= 36; i++) {
      const elements = [
        `PPC${i}`,
        `PMT${i}`,
        `PPT${i}`,
        `T_Type${i}`,
        `P_Type${i}`,
        `S_Type${i}`,
        `PPV${i}`,
        `RMT${i}`,
        `RPT${i}`,
        `RPN${i}`,
      ];

      elements.forEach((id) => {
        const element = document.getElementById(id);
        if (element) {
          element.disabled = !status;
        }
      });
    }
  };

  const [columnVisibility, setColumnVisibility] = useState({
    Quoteinfo: false,
    ASP_Schedule: false,
    Schedule_BK_RE: false,
    PI_Machine: false,
    PI_Person: false,
  });

  const allColumns = Object.keys(columnVisibility);
  const isAllSelected = allColumns.every((key) => columnVisibility[key]);

  const toggleAllColumns = () => {
    const newValue = !isAllSelected;
    const updated = {};
    allColumns.forEach((key) => {
      updated[key] = newValue;
    });
    setColumnVisibility(updated);
  };

  const toggleColumn = (columnKey) => {
    setColumnVisibility((prev) => ({
      ...prev,
      [columnKey]: !prev[columnKey],
    }));
  };

  const [showOrderForm, setshowOrderForm] = useState(true);
  const [showPlanForm, setshowPlanForm] = useState(true);

  const toggleOrderForm = () => {
    setshowOrderForm((prev) => !prev);
  };

  const togglePlanForm = () => {
    setshowPlanForm((prev) => !prev);
  };

  const [scale, setScale] = useState(1);
  const [scaleOrderinfo, setScaleOrderinfo] = useState(1);
  const [scalePlaninfo, setScalePlaninfo] = useState(1);
  const [translateYFactor, setTranslateYFactor] = useState(0);
  const [marginBottom, setMarginBottom] = useState(0);
  const [translateYOrder, setTranslateYOrder] = useState(0);
  const [translateYPlan, setTranslateYPlan] = useState(0);

  useEffect(() => {
    const updateScale = () => {
      const screenWidth = window.innerWidth;
      const baseWidth = 1920;

      const ratio = screenWidth / baseWidth;
      const newScale = Math.max(0.2, Math.min(1, ratio * 1.3));
      const newScaleOrderinfo = Math.max(0.2, Math.min(1, ratio * 1.073));
      const newScalePlaninfo = Math.max(0.2, Math.min(1, ratio * 1.023));

      const newTranslateYFactor = (() => {
        let baseFactor = 0.25;

        if (!showOrderForm && !showPlanForm) {
          baseFactor = 0.033;
        } else if (!showPlanForm) {
          baseFactor = 0.1;
        } else if (!showOrderForm) {
          baseFactor = 0.17;
        }

        let baseTranslate = Math.min(0, (screenWidth - baseWidth) * baseFactor);

        if (screenWidth < 960) {
          baseTranslate *= 1;
        }

        return baseTranslate;
      })();

      const newMarginBottom = (() => {
        let baseMargin = 0.22;

        if (!showOrderForm && !showPlanForm) {
          baseMargin = 0.01;
        } else if (!showPlanForm) {
          baseMargin = 0.1;
        } else if (!showOrderForm) {
          baseMargin = 0.15;
        }

        let baseMarginChange = Math.min(
          0,
          (screenWidth - baseWidth) * baseMargin
        );

        if (screenWidth < 960) {
          baseMarginChange *= 0.5;
        }

        return baseMarginChange;
      })();

      const newTranslateYOrder = Math.min(0, (screenWidth - baseWidth) * 0.01);
      const newTranslateYPlan = (() => {
        if (!showOrderForm) return -20;
        let factor = 0.095;
        return Math.min(0, (screenWidth - baseWidth) * factor);
      })();

      setScale(newScale);
      setScaleOrderinfo(newScaleOrderinfo);
      setScalePlaninfo(newScalePlaninfo);
      setTranslateYFactor(newTranslateYFactor);
      setMarginBottom(newMarginBottom);
      setTranslateYOrder(newTranslateYOrder);
      setTranslateYPlan(newTranslateYPlan);
    };

    updateScale();
    window.addEventListener("resize", updateScale);

    return () => window.removeEventListener("resize", updateScale);
  }, [showOrderForm, showPlanForm]);

  return (
    <div className="flex bg-[#E9EFEC] h-screen ">
      <Sidebar />
      <div className="flex flex-col w-screen mr-2 ml-2">
        <div className="flex-col overflow-hidden overflow-y-auto p-2"  style={{ height: '100%' }}>
          <div className="bg-white grid grid-cols-1" style={{ height: '100%', overflowY: 'scroll' }}>
            <div className="flex justify-center items-center" style={{ height: 'fit-content' }}>
              <h1 className="text-xl font-bold">Plan Info</h1>
            </div>

            {/* Popup Material When Click New Add */}
            {showPopup && (
              <div className="fixed inset-0 z-50 bg-black bg-opacity-50 flex justify-center items-center h-screen p-4 sm:p-8">
                <div className="bg-white p-6 rounded-lg shadow-lg max-w-lg w-full">
                  <h2 className="text-xl font-bold mb-4 text-center">
                    Select Material
                  </h2>

                  <div className="space-y-4">
                    {MaterialOptions.map((material) => (
                      <label
                        key={material}
                        className="flex items-center gap-3 sm:gap-4 text-sm sm:text-base"
                      >
                        <input
                          type="radio"
                          name="material"
                          value={material}
                          checked={selectedMates === material}
                          onChange={handleCheckboxChange}
                          className="w-5 h-5"
                        />
                        {material} Paste
                      </label>
                    ))}
                  </div>

                  <div className="flex justify-center gap-4 mt-6">
                    <button
                      onClick={handleConfirm}
                      className="px-6 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition"
                    >
                      OK
                    </button>
                    <button
                      onClick={() => setShowPopup(false)}
                      className="px-6 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              </div>
            )}
            {/* End Popup Material */}

            {/* Popup DoubleClick Process In Table */}
            {/* แสดง Popup เฉพาะ ID ที่ถูกดับเบิ้ลคลิก */}
            {Line_Edit_View && selectedId && (
              <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                <div className="bg-white p-6 rounded-xl shadow-xl max-w-3xl w-full scale-75">
                  <h3 className="text-xl font-semibold text-left text-gray-700 mb-4">
                    Edit_CAT
                  </h3>

                  <div className="grid grid-cols-3 gap-2 ">
                    <div className="flex flex-row col-span-2 gap-4 justify-center">
                      <div className="flex flex-col  ">
                        <p className="mb-2">Target_Process_No</p>
                        <div className="border-2 p-4 rounded-lg">
                          <div className="flex items-center mb-4 gap-2">
                            <input
                              type="checkbox"
                              checked={
                                planData?.Edit_CAT1 !== undefined
                                  ? planData.Edit_CAT1
                                  : true
                              }
                              id="Edit_CAT1"
                              onChange={(e) => {
                                if (!planData?.Edit_CAT1) {
                                  handlePlanInputChange(e);
                                }
                              }}
                              className="h-4 w-4 border-2 rounded-full appearance-none checked:bg-blue-500"
                            />
                            <label htmlFor="checkbox1" className="text-xs mr-2">
                              1Line_Insert
                            </label>
                          </div>

                          <div className="flex items-center mb-4 gap-2">
                            <input
                              type="checkbox"
                              checked={
                                planData?.Edit_CAT2 !== undefined
                                  ? planData.Edit_CAT2
                                  : false
                              }
                              id="Edit_CAT2"
                              onChange={(e) => {
                                if (!planData?.Edit_CAT2) {
                                  handlePlanInputChange(e);
                                }
                              }}
                              className="h-4 w-4 border-2 rounded-full appearance-none checked:bg-blue-500"
                            />
                            <label htmlFor="checkbox2" className="text-xs mr-2">
                              1Line_Delete
                            </label>
                          </div>

                          <div className="flex items-center mb-4 gap-2">
                            <input
                              type="checkbox"
                              checked={
                                planData?.Edit_CAT3 !== undefined
                                  ? planData.Edit_CAT3
                                  : false
                              }
                              id="Edit_CAT3"
                              onChange={(e) => {
                                if (!planData?.Edit_CAT3) {
                                  handlePlanInputChange(e);
                                }
                              }}
                              className="h-4 w-4 border-2 rounded-full appearance-none checked:bg-blue-500"
                            />
                            <label htmlFor="checkbox3" className="text-xs mr-2">
                              1Line_Move
                            </label>
                          </div>

                          <div className="flex items-center mb-4 gap-2">
                            <input
                              type="checkbox"
                              checked={
                                planData?.Edit_CAT4 !== undefined
                                  ? planData.Edit_CAT4
                                  : false
                              }
                              id="Edit_CAT4"
                              onChange={(e) => {
                                if (!planData?.Edit_CAT4) {
                                  handlePlanInputChange(e);
                                }
                              }}
                              className="h-4 w-4 border-2 rounded-full appearance-none checked:bg-blue-500"
                            />
                            <label htmlFor="checkbox4" className="text-xs mr-2">
                              1line_Copy/Paste
                            </label>
                          </div>
                        </div>
                      </div>

                      <div className="flex flex-col ">
                        <input
                          id="Target_Pr_No"
                          value={
                            planData?.Target_Pr_No !== undefined
                              ? planData.Target_Pr_No
                              : selectedId || ""
                          }
                          type="text"
                          onChange={handlePlanInputChange}
                          className="mb-2 border border-black rounded px-2 py-1 text-xs w-8"
                        />
                        <div className="flex items-center mb-2 gap-2">
                          <input
                            disabled={planData?.Edit_CAT1 === false}
                            id="Insert_Pr_No"
                            value={
                              planData?.Insert_Pr_No !== undefined
                                ? planData.Insert_Pr_No
                                : selectedId || ""
                            }
                            type="text"
                            onChange={handlePlanInputChange}
                            className={`border border-black rounded px-2 py-1 text-xs w-8 ${
                              planData?.Edit_CAT1 === false
                                ? "bg-gray-400 cursor-not-allowed"
                                : ""
                            }`}
                          />
                          <label
                            htmlFor="Insert_Pr_No"
                            className="text-xs mr-2"
                          >
                            Insert_Line
                          </label>
                        </div>
                        <div className="flex items-center mb-2 gap-2">
                          <input
                            disabled={planData?.Edit_CAT2 === false}
                            id="Delete_Pr_No"
                            value={
                              planData?.Delete_Pr_No !== undefined
                                ? planData.Delete_Pr_No
                                : selectedId || ""
                            }
                            type="text"
                            onChange={handlePlanInputChange}
                            className={`border border-black rounded px-2 py-1 text-xs w-8 ${
                              planData?.Edit_CAT2 === false
                                ? "bg-gray-400 cursor-not-allowed"
                                : ""
                            }`}
                          />
                          <label
                            htmlFor="Delete_Pr_No"
                            className="text-xs mr-2"
                          >
                            Delete_Line
                          </label>
                        </div>

                        <div className="flex items-center mb-2 gap-2">
                          <input
                            disabled={planData?.Edit_CAT3 === false}
                            id="Mv_Cut_Pr_No"
                            value={
                              planData?.Mv_Cut_Pr_No !== undefined
                                ? planData.Mv_Cut_Pr_No
                                : selectedId || ""
                            }
                            type="text"
                            onChange={handlePlanInputChange}
                            className={`border border-black rounded px-2 py-1 text-xs w-8 ${
                              planData?.Edit_CAT3 === false
                                ? "bg-gray-400 cursor-not-allowed"
                                : ""
                            }`}
                          />
                          <label
                            htmlFor="Mv_Cut_Pr_No To"
                            className="text-xs mr-2"
                          >
                            Move To
                          </label>
                          <input
                            disabled={planData?.Edit_CAT3 === false}
                            id="Mv_Paste_Pr_No"
                            value={
                              planData?.Mv_Paste_Pr_No !== undefined
                                ? planData.Mv_Paste_Pr_No
                                : ""
                            }
                            type="text"
                            onChange={handlePlanInputChange}
                            className={`border border-black rounded px-2 py-1 text-xs w-8 ${
                              planData?.Edit_CAT3 === false
                                ? "bg-gray-400 cursor-not-allowed"
                                : ""
                            }`}
                          />
                          <label
                            htmlFor="Mv_Paste_Pr_No"
                            className="text-xs mr-2"
                          >
                            No_Line
                          </label>
                        </div>
                        <div className="flex items-center  mb-2 gap-2">
                          <input
                            disabled={planData?.Edit_CAT4 === false}
                            id="Cp_Copy_Pr_No"
                            value={
                              planData?.Cp_Copy_Pr_No !== undefined
                                ? planData.Cp_Copy_Pr_No
                                : selectedId || ""
                            }
                            type="text"
                            onChange={handlePlanInputChange}
                            className={`border border-black rounded px-2 py-1 text-xs w-8 ${
                              planData?.Edit_CAT4 === false
                                ? "bg-gray-400 cursor-not-allowed"
                                : ""
                            }`}
                          />
                          <label
                            htmlFor="Cp_Copy_Pr_No"
                            className="text-xs mr-2"
                          >
                            Copy_To
                          </label>
                          <input
                            disabled={planData?.Edit_CAT4 === false}
                            id="Cp_Paste_Pr_No"
                            value={
                              planData?.Cp_Paste_Pr_No !== undefined
                                ? planData.Cp_Paste_Pr_No
                                : ""
                            }
                            type="text"
                            onChange={handlePlanInputChange}
                            className={`border border-black rounded px-2 py-1 text-xs w-8 ${
                              planData?.Edit_CAT4 === false
                                ? "bg-gray-400 cursor-not-allowed"
                                : ""
                            }`}
                          />
                          <label
                            htmlFor="Cp_Paste_Pr_No"
                            className="text-xs mr-2"
                          >
                            No_Line
                          </label>
                        </div>
                      </div>
                    </div>

                    <div className="flex flex-col justify-center items-center space-y-2">
                      <button
                        onClick={Execute_Click}
                        className="bg-blue-500 text-white px-4 py-3 rounded-full hover:bg-blue-600 transition duration-300 ease-in-out w-48"
                      >
                        Execute
                      </button>
                      <button
                        onClick={handleClosePopup}
                        className="bg-red-500 text-white px-4 py-3 rounded-full hover:bg-red-600 transition duration-300 ease-in-out w-48"
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}
            {/* End Popup  */}

            <div className="w-full bg-white sticky -top-2 z-40" style={{ height: 'fit-content' }}>
              <div
                style={{
                  transform: `scale(${scale})`, 
                  transformOrigin: "top left",
                  transition: "transform 0.3s ease",
                }}
              >
                <div className="px-2">
                  <div className="flex items-center space-x-2 justify-start">
                    <label className="text-xs font-medium">Date:</label>
                    <input
                      type="text"
                      className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-32"
                      value={new Date().toLocaleDateString("en-EN", {
                        day: "2-digit",
                        month: "2-digit",
                        year: "numeric",
                      })}
                      readOnly
                    />
                    <div className="inline-flex space-x-0">
                      <button
                        onClick={GRAPH_Click}
                        className="bg-gray-300 px-2 py-1 rounded-l text-xs"
                      >
                        GRA
                      </button>
                      <button
                        onClick={List_Click}
                        className="bg-gray-300 px-2 py-1 rounded-r text-xs"
                      >
                        List
                      </button>
                    </div>
                    <div className="flex flex-row space-x-4 min-w-[300px] w-full px-1 py-1">
                      {/* บรรทัดของ Date */}
                      <div className="flex items-center space-x-2">
                        <label className="text-xs font-medium">Date:</label>
                        <input
                          type="date"
                          className="border-2 border-gray-500 rounded-md px-2 py-1 text-xs"
                        />
                      </div>

                      {/* บรรทัดของ Checkbox */}
                      <div className="flex items-center space-x-2">
                        <input
                          id="Auto_Year_Change"
                          checked={autoYearChange}
                          onChange={() => setAutoYearChange(!autoYearChange)}
                          type="checkbox"
                          className="form-checkbox border-gray-400 rounded"
                        />
                        <label className="text-xs font-medium">
                          Auto_Year_Change
                        </label>
                      </div>

                      {/* บรรทัดของ Target_PrG */}
                      <div className="flex items-center space-x-2">
                        <label className="text-xs font-medium">
                          Target_PrG
                        </label>
                        <select
                          id="Tg_ProcessG"
                          value={planData?.Tg_ProcessG || ""}
                          onChange={handlePlanInputChange}
                          className="border-2 border-gray-500 rounded-md bg-[#ccffff] px-2 py-1 text-xs w-24"
                        >
                          <option value=""></option>
                          {Array.isArray(ProcessGData) &&
                          ProcessGData.length > 0 ? (
                            ProcessGData.map((item, index) => (
                              <option key={index} value={item.ProcessG_Mark}>
                                {item.ProcessG_Mark}
                              </option>
                            ))
                          ) : (
                            <option value="">No data found</option>
                          )}
                        </select>
                      </div>

                      {/* บรรทัดของ Target_Date */}
                      <div className="flex items-center space-x-2">
                        <label className="text-xs font-medium">
                          Target_Date
                        </label>
                        <input
                          id="Tg_St_Pl_Date"
                          value={
                            planData?.[`Tg_St_Pl_Date`]
                              ? new Date(planData[`Tg_St_Pl_Date`])
                                  .toISOString()
                                  .split("T")[0]
                              : ""
                          }
                          onChange={handlePlanInputChange}
                          type="date"
                          className="border-2 border-gray-500 rounded-md bg-[#ccffff] px-2 py-1 text-xs"
                        />
                        <span className="text-xs font-medium">~</span>
                        <input
                          id="Tg_Ed_Pl_Date"
                          value={
                            planData?.[`Tg_Ed_Pl_Date`]
                              ? new Date(planData[`Tg_Ed_Pl_Date`])
                                  .toISOString()
                                  .split("T")[0]
                              : new Date(
                                  new Date().setDate(new Date().getDate() + 10)
                                )
                                  .toISOString()
                                  .split("T")[0]
                          }
                          onChange={handlePlanInputChange}
                          type="date"
                          className="border-2 border-gray-500 rounded-md bg-[#ccffff] px-2 py-1 text-xs"
                        />
                      </div>
                    </div>
                  </div>

                  <div className="flex space-x-4">
                    <div className="flex items-center space-x-2">
                      <label className="text-xs font-medium">
                        Search_Order_No
                      </label>
                      <input
                        ref={SearchorderNoRef}
                        id="Search_Order_No"
                        type="text"
                        value={searchOrderNo || ""}
                        onChange={handleInputChange}
                        className="border-2 border-gray-500 rounded-md px-2 py-1 text-xs bg-[#ccffff] w-32"
                        onBlur={handleSearchOrderNoBlur}
                        onKeyDown={async (e) => {
                          if (e.key === "Enter") {
                            handleSearch(); // เรียก API เมื่อกด Enter
                          }
                        }}
                      />
                    </div>

                    <div className="flex items-center space-x-2">
                      <label className="text-xs font-medium">
                        Search_Parts_No
                      </label>
                      <Select
                        id="Search_Parts_No"
                        ref={SearchPartsNoRef}
                        value={
                          searchPlanNo
                            ? { label: searchPlanNo, value: searchPlanNo }
                            : null
                        }
                        onChange={(selectedOption) =>
                          handlePlanInputChange(
                            null,
                            selectedOption,
                            "Search_Parts_No"
                          )
                        }
                        options={
                          Array.isArray(selectedPlanNo) &&
                          selectedPlanNo.length > 0
                            ? selectedPlanNo.map((item) => ({
                                label: item.Parts_No,
                                value: item.Parts_No,
                              }))
                            : [{ label: "No data found", value: "" }]
                        }
                        className="text-xs w-28"
                        styles={{
                          control: (base) => ({
                            ...base,
                            borderColor: "gray",
                            backgroundColor: "#ccffff",
                          }),
                          menu: (base) => ({
                            ...base,
                            fontSize: "12px",
                          }),
                          option: (base) => ({
                            ...base,
                            fontSize: "12px",
                          }),
                        }}
                        isDisabled={!searchOrderNo}
                        menuPortalTarget={document.body}
                        noOptionsMessage={() => "No data found"}
                        placeholder="Part No"
                      />
                    </div>

                    <div className="flex items-center space-x-2">
                      <label className="text-xs font-medium">
                        Search_Odpt_No
                      </label>
                      <input
                        disabled
                        id="Search_Odpt_No"
                        value={Search_Odpt_No || ""}
                        onChange={(e) => handlePlanInputChange(e)}
                        type="text"
                        className="border-2 border-gray-500 rounded-md px-2 py-1 text-xs w-28"
                      />
                    </div>

                    <div className="flex items-center space-x-2">
                      <label className="text-xs font-medium">Order_No</label>
                      <input
                        disabled
                        value={planData?.Order_No || ""}
                        onChange={handlePlanInputChange}
                        type="text"
                        className="border-2 border-gray-500 rounded-md px-2 py-1 text-xs w-28"
                      />
                    </div>

                    <div className="flex items-center space-x-2">
                      <label className="text-xs font-medium">Odpt_No</label>
                      <input
                        disabled
                        id="OdPt_No"
                        value={planData?.OdPt_No || ""}
                        onChange={handlePlanInputChange}
                        type="text"
                        className="border-2 border-gray-500 rounded-md px-2 py-1 text-xs w-28"
                      />
                    </div>

                    <div className="flex space-x-1">
                      <button
                        onClick={PP_View_Click}
                        className="bg-gray-300 py-1 px-2 rounded text-xs"
                      >
                        PP
                      </button>
                      <button
                        onClick={PD_View_Click}
                        className="bg-gray-300 py-1 px-2 rounded text-xs"
                      >
                        PD
                      </button>
                      <button
                        onClick={RD_View_Click}
                        className="bg-gray-300  py-1 px-2 rounded text-xs"
                      >
                        RD
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <hr
                className="border-y-[1px] border-gray-300 lg:mt-2"
                style={{
                  transform: `translateY(${translateYOrder}px)`,
                }}
              />
            </div>

            <div className="w-full" style={{ height: 'fit-content' }}>
              <div className="flex flex-wrap">
                {/* Start Order Form */}
                <div
                  className="pl-5"
                  style={{
                    transform: `scale(${scaleOrderinfo}) translateY(${translateYOrder}px)`,
                    transformOrigin: "top left",
                    transition: "transform 0.3s ease",
                  }}
                >
                  {/* Head Order_info */}
                  <div className="flex items-center font-medium my-2">
                    <label>[Order_info]</label>

                    <button
                      onClick={toggleOrderForm}
                      className="ml-5 p-1 border border-gray-400 rounded hover:bg-gray-100 transition"
                    >
                      {showOrderForm ? (
                        <FaEyeSlash size={20} />
                      ) : (
                        <FaEye size={20} />
                      )}
                    </button>
                  </div>
                  {showOrderForm && (
                    <div>
                      {/* Group 1 */}
                      <div className="flex flex-row gap-2 pb-1 justify-start">
                        <div className="flex gap-2">
                          <label className="text-xs pr-1">Od_No</label>
                          <input
                            disabled
                            id="Order_No"
                            value={orderData?.Order_No ?? ""}
                            onChange={handleInputChange}
                            type="text"
                            className="bg-whtie border-solid border-2 border-gray-500 rounded-md px-1 w-28"
                          />
                        </div>
                        <div className="flex gap-2">
                          <label className="text-xs">Product_Grp</label>
                          <select
                            id="Product_Grp_CD"
                            onChange={handleInputChange}
                            value={orderData?.Product_Grp_CD ?? ""}
                            className="border-2 border-gray-500 rounded-md px-2 py-1 text-xs bg-[#ffff99] w-20"
                          >
                            <option value={orderData?.Product_Grp_CD ?? ""}>
                              {orderData?.Product_Grp_CD ?? ""}
                            </option>
                            {Array.isArray(WorkgData) &&
                            WorkgData.length > 0 ? (
                              WorkgData.map((item, index) => (
                                <option key={index} value={item.WorkG_CD}>
                                  {item.WorkG_CD}
                                </option>
                              ))
                            ) : (
                              <option value="">No data found</option>
                            )}
                          </select>
                        </div>
                        <div className="flex gap-2 pl-20">
                          <label className="text-xs">Mate1</label>
                          <div className="flex gap-1">
                            <input
                              disabled
                              id="Material1"
                              value={orderData?.Material1 ?? ""}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-whtie border-solid border-2 border-gray-500 rounded-md px-1 w-20"
                            />

                            <input
                              disabled
                              id="H_Treatment1"
                              value={orderData?.H_Treatment1 ?? ""}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-whtie border-solid border-2 border-gray-500 rounded-md px-1 w-20"
                            />
                          </div>
                        </div>
                        <div className="flex w-auto">
                          <label className="w-10 text-xs">PO No</label>
                          <div className="flex mr-1">
                            <input
                              type="text"
                              id="Od_No_of_Custom"
                              value={orderData?.Od_No_of_Custom ?? ""}
                              onChange={handleInputChange}
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-20"
                              readOnly
                            />
                          </div>
                        </div>
                        <div className="flex gap-2 w-auto ">
                          <label className="text-xs">Od_Ctl_Person</label>
                          <div className="flex gap-1">
                            <select
                              id="Od_Ctl_Person_CD"
                              value={orderData?.Od_Ctl_Person_CD ?? ""}
                              onChange={handleInputChange}
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-xs bg-[#ffff99] w-20"
                            >
                              <option value={orderData?.Od_Ctl_Person_CD || ""}>
                                {orderData?.Od_Ctl_Person_CD || ""}
                              </option>
                              {Array.isArray(WorkerData) &&
                              WorkerData.length > 0 ? (
                                WorkerData.map((item, index) => (
                                  <option key={index} value={item.Worker_CD}>
                                    {item.Worker_CD}
                                  </option>
                                ))
                              ) : (
                                <option value="">No data found</option>
                              )}
                            </select>
                            <input
                              disabled
                              value={selectedSalesPersonAbb ?? ""}
                              onChange={(event) => setWorkerData(event)}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-20"
                            />
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <label className="text-xs">Sales</label>
                          <div className="flex gap-1">
                            <input
                              disabled
                              value={orderData?.Sales_Person_CD ?? ""}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-16"
                            />
                          </div>
                        </div>
                        <div className="flex gap-2 pl-7">
                          <label className="text-xs">Specific</label>
                          <div className="flex gap-1">
                            <select
                              id="Specific_CD"
                              value={orderData?.Specific_CD ?? ""}
                              onChange={handleInputChange}
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-xs bg-[#ffff99] w-20"
                            >
                              <option value={orderData?.Specific_CD || ""}>
                                {orderData?.Specific_CD || ""}
                              </option>
                              {Array.isArray(SpecificData) &&
                              SpecificData.length > 0 ? (
                                SpecificData.map((item, index) => (
                                  <option key={index} value={item.Specific_CD}>
                                    {item.Specific_CD}
                                  </option>
                                ))
                              ) : (
                                <option value="">No data found</option>
                              )}
                            </select>
                            <input
                              disabled
                              value={SpecificName ?? ""}
                              onChange={(event) => setSpecificData(event)}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-20"
                            />
                          </div>
                        </div>
                        <div className="flex gap-2 pl-2">
                          <label className="text-xs">Pd_Receive</label>
                          <div className="">
                            <input
                              disabled
                              id="Pd_Received_Date"
                              value={formatDate(
                                orderData?.Pd_Received_Date ?? ""
                              )}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-[100px]"
                            />
                          </div>
                        </div>
                      </div>

                      {/* Group 2 */}
                      <div className="flex flex-row gap-2 pb-1 justify-start">
                        <div className="flex gap-2">
                          <label className="text-xs">Request</label>
                          <input
                            readOnly
                            id="Request_Delivery"
                            value={formatDate(orderData?.Request_Delivery)}
                            type="text"
                            className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-28"
                          />
                        </div>
                        <div className="flex gap-2">
                          <label className="text-xs pl-3.5">Customer</label>
                          <div className="flex gap-1">
                            <select
                              id="Customer_CD"
                              value={orderData?.Customer_CD ?? ""}
                              onChange={handleInputChange}
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-xs bg-[#ffff99] w-20"
                            >
                              <option value={orderData?.Customer_CD || ""}>
                                {orderData?.Customer_CD || ""}
                              </option>
                              {Array.isArray(CustomerData) &&
                              CustomerData.length > 0 ? (
                                CustomerData.map((item, index) => (
                                  <option key={index} value={item.Customer_CD}>
                                    {item.Customer_CD}
                                  </option>
                                ))
                              ) : (
                                <option value="">No data found</option>
                              )}
                            </select>
                            <input
                              disabled
                              value={selectedCustomerAbb ?? ""}
                              onChange={(event) => setCustomerData(event)}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-[75px]"
                            />
                          </div>
                        </div>
                        <div className="flex gap-2 w-auto">
                          <label className="w-auto text-xs">Mate2</label>
                          <div className="w-auto flex gap-1">
                            <input
                              disabled
                              id="Material2"
                              value={orderData?.Material2 ?? ""}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-whtie border-solid border-2 border-gray-500 rounded-md px-1 w-20"
                            />

                            <input
                              disabled
                              id="H_Treatment2"
                              value={orderData?.H_Treatment2 ?? ""}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-whtie border-solid border-2 border-gray-500 rounded-md px-1 w-20"
                            />
                          </div>
                        </div>
                        <div className="flex gap-3.5">
                          <label className="text-xs">Req3</label>
                          <div className="flex gap-1">
                            <select
                              id="Request3_CD"
                              onChange={handleInputChange}
                              value={orderData?.Request3_CD ?? ""}
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-xs bg-[#ffff99] w-20"
                            >
                              <option value={orderData?.Request3_CD || ""}>
                                {orderData?.Request3_CD || ""}
                              </option>
                              {Array.isArray(Request3Data) &&
                              Request3Data.length > 0 ? (
                                Request3Data.map((item, index) => (
                                  <option key={index} value={item.Request3_CD}>
                                    {item.Request3_CD}
                                  </option>
                                ))
                              ) : (
                                <option value="">No data found</option>
                              )}
                            </select>
                          </div>
                        </div>
                        <div className="flex gap-3 pl-9">
                          <label className="text-xs">Coating</label>
                          <div className="flex gap-1">
                            <select
                              id="Coating_CD"
                              value={orderData?.Coating_CD ?? ""}
                              onChange={handleInputChange}
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-xs bg-[#ffff99] w-20"
                            >
                              <option value={orderData?.Coating_CD || ""}>
                                {orderData?.Coating_CD || ""}
                              </option>
                              {Array.isArray(CoatingData) &&
                              CoatingData.length > 0 ? (
                                CoatingData.map((item, index) => (
                                  <option key={index} value={item.Coating_CD}>
                                    {item.Coating_CD}
                                  </option>
                                ))
                              ) : (
                                <option value="">No data found</option>
                              )}
                            </select>
                            <input
                              disabled
                              value={coatingName ?? ""}
                              onChange={(event) => setCoatingData(event)}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-20"
                            />
                          </div>
                        </div>
                        <div className="flex gap-1">
                          <label className="text-xs">Detail</label>
                          <div className="flex">
                            <input
                              disabled
                              id="Coating"
                              value={orderData?.Coating ?? ""}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-16"
                            />
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <label className="text-xs">Od_Progress</label>
                          <div className="flex gap-1">
                            <select
                              id="Od_Progress_CD"
                              value={orderData?.Od_Progress_CD ?? ""}
                              onChange={handleInputChange}
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-xs bg-[#ffff99] w-20"
                            >
                              <option value={orderData?.Od_Progress_CD || ""}>
                                {orderData?.Od_Progress_CD || ""}
                              </option>
                              {Array.isArray(OdProgressData) &&
                              OdProgressData.length > 0 ? (
                                OdProgressData.map((item, index) => (
                                  <option
                                    key={index}
                                    value={item.Od_Progress_CD}
                                  >
                                    {item.Od_Progress_CD}
                                  </option>
                                ))
                              ) : (
                                <option value="">No data found</option>
                              )}
                            </select>
                            <input
                              disabled
                              value={OdProgressName ?? ""}
                              onChange={(event) => setOdProgressData(event)}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-20"
                            />
                          </div>
                        </div>
                        <div className="flex gap-2 pl-4">
                          <label className="text-xs">Pd_Comp</label>
                          <input
                            disabled
                            id="Pd_Complete_Date"
                            value={formatDate(
                              orderData?.Pd_Complete_Date ?? ""
                            )}
                            onChange={handleInputChange}
                            type="text"
                            className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-[100px]"
                          />
                        </div>
                      </div>

                      {/* Group 3 */}
                      <div className="flex flex-row gap-2 pb-1 justify-start">
                        <div className="flex gap-2">
                          <label className="text-xs">Product</label>
                          <div className="relative">
                            <DatePicker
                              ref={productDeliDateRef}
                              key="productDeliDate"
                              selected={odDates.productDeliDate}
                              dateFormat="dd/MM/yyyy"
                              onChange={(date) =>
                                setOdDates((prev) => ({
                                  ...prev,
                                  productDeliDate: date,
                                }))
                              }
                              onBlur={handleProductDeliDateBlur}
                              popperPlacement="bottom-start"
                              portalId="root-portal"
                              inline={false}
                              disabled={!isEditedOrderDate}
                              className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 max-w-28 cursor-pointer"
                            />
                            <AiTwotoneCalendar
                              className="absolute right-2 top-1.5 text-gray-800 cursor-pointer"
                              size={16}
                              onClick={() =>
                                handleOpenDatePicker(productDeliDateRef)
                              }
                            />
                          </div>
                        </div>
                        <div className="flex gap-2 pl-[18px]">
                          <label className="text-xs">Pd_name</label>
                          <input
                            disabled
                            id="Product_Name"
                            value={orderData?.Product_Name ?? ""}
                            onChange={handleInputChange}
                            type="text"
                            className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 w-40"
                          />
                        </div>
                        <div className="flex gap-2">
                          <label className="text-xs">Mate3</label>
                          <div className="flex gap-1">
                            <input
                              disabled
                              id="Material3"
                              value={orderData?.Material3 ?? ""}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-whtie border-solid border-2 border-gray-500 rounded-md px-1 w-20"
                            />

                            <input
                              disabled
                              id="H_Treatment3"
                              value={orderData?.H_Treatment3 ?? ""}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-whtie border-solid border-2 border-gray-500 rounded-md px-1 w-20"
                            />
                          </div>
                        </div>
                        <div className="flex">
                          <label className="text-xs">Qty</label>
                          <div className="flex gap-1 mr-1 pl-5">
                            <input
                              disabled
                              id="Quantity"
                              value={orderData?.Quantity ?? ""}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-whtie border-solid border-2 border-gray-500 rounded-md px-1 w-14"
                            />
                            <input
                              disabled
                              id="Unit_CD"
                              value={
                                Array.isArray(UnitsData) && orderData?.Unit_CD
                                  ? UnitsData.find(
                                      (unit) =>
                                        unit.Unit_CD === orderData.Unit_CD
                                    )?.Unit_Abb || orderData.Unit_CD
                                  : ""
                              }
                              onChange={handleInputChange}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-14"
                            />
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <div className="flex gap-1">
                            <input
                              id="Od_Pending"
                              type="checkbox"
                              checked={planData?.Od_Pending}
                              onChange={handlePlanInputChange}
                              className="form-checkbox border-gray-400 rounded"
                            />
                            <label className="text-xs mt-1">Od_Pending</label>
                          </div>
                        </div>
                        <div className="flex gap-2 pl-6">
                          <label className="text-xs mt-1">Product_Docu</label>
                          <div className="flex gap-1">
                            <input
                              id="Product_Docu"
                              value={orderData?.Product_Docu ?? ""}
                              onChange={handleInputChange}
                              type="text"
                              readOnly // ป้องกันการแก้ไขถ้าต้องการให้คลิกอย่างเดียว
                              className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 w-[126px] cursor-pointer"
                              onClick={() => {
                                if (orderData?.Product_Docu) {
                                  handlePathClick(orderData.Product_Docu);
                                } else {
                                  alert("ไม่มีลิงก์ให้เปิด");
                                }
                              }}
                            />
                          </div>
                        </div>
                        <div className="flex gap-2 w-auto pl-[26px]">
                          <label className="w-10 text-xs">Delivery</label>
                          <div className="w-auto flex gap-1">
                            <select
                              id="Delivery_CD"
                              value={orderData?.Delivery_CD ?? ""}
                              onChange={handleInputChange}
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-xs bg-[#ffff99] w-20"
                            >
                              <option value={orderData?.Delivery_CD || ""}>
                                {orderData?.Delivery_CD || ""}
                              </option>
                              {Array.isArray(DeliveryData) &&
                              DeliveryData.length > 0 ? (
                                DeliveryData.map((item, index) => (
                                  <option key={index} value={item.Delivery_CD}>
                                    {item.Delivery_CD}
                                  </option>
                                ))
                              ) : (
                                <option value="">No data found</option>
                              )}
                            </select>
                            <input
                              disabled
                              value={DeliveryName ?? ""}
                              onChange={(event) => setDeliveryData(event)}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-20"
                            />
                          </div>
                        </div>
                        <div className="flex gap-2 pl-3.5">
                          <label className="text-xs">QC_Comp</label>
                          <input
                            disabled
                            id="I_Completed_Date"
                            value={formatDate(
                              orderData?.I_Completed_Date ?? ""
                            )}
                            onChange={handleInputChange}
                            type="text"
                            className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-[100px]"
                          />
                        </div>
                      </div>

                      {/* Group 4 */}
                      <div className="flex flex-row gap-2 pb-1 justify-start">
                        <div className="flex gap-2">
                          <label className="text-xs">Confirm</label>
                          <div className="relative">
                            <DatePicker
                              ref={confirmDeliDateRef}
                              key="confirmDeliDate"
                              selected={odDates.confirmDeliDate}
                              dateFormat="dd/MM/yyyy"
                              onChange={(date) =>
                                setOdDates((prev) => ({
                                  ...prev,
                                  confirmDeliDate: date,
                                }))
                              }
                              onBlur={handleConfirmDeliDateBlur}
                              popperPlacement="bottom-start"
                              portalId="root-portal"
                              inline={false}
                              disabled={!isEditedOrderDate}
                              className="bg-[#ff99cc] border-solid border-2 border-gray-500 rounded-md px-1 max-w-28 cursor-pointer"
                            />
                            <AiTwotoneCalendar
                              className="absolute right-2 top-1.5 text-gray-800 cursor-pointer"
                              size={16}
                              onClick={() =>
                                handleOpenDatePicker(confirmDeliDateRef)
                              }
                            />
                          </div>
                        </div>
                        <div className="flex gap-2 pl-6">
                          <label className="text-xs">Pd_Size</label>
                          <input
                            disabled
                            id="Product_Size"
                            value={orderData?.Product_Size ?? ""}
                            onChange={handleInputChange}
                            type="text"
                            className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 w-40"
                          />
                        </div>
                        <div className="flex gap-2 pl-0.5">
                          <label className="text-xs">Mate4</label>
                          <div className="flex gap-1">
                            <input
                              disabled
                              id="Material4"
                              value={orderData?.Material4 ?? ""}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-whtie border-solid border-2 border-gray-500 rounded-md px-1 w-20"
                            />

                            <input
                              disabled
                              id="H_Treatment4"
                              value={orderData?.H_Treatment4 ?? ""}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-whtie border-solid border-2 border-gray-500 rounded-md px-1 w-20"
                            />
                          </div>
                        </div>
                        <div className="flex">
                          <label className="text-xs">Price</label>
                          <div className="flex gap-1 pl-3">
                            <select
                              id="Price_CD"
                              value={orderData?.Price_CD ?? ""}
                              onChange={handleInputChange}
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-xs bg-[#ffff99] w-14"
                            >
                              <option value={orderData?.Price_CD || ""}>
                                {orderData?.Price_CD || ""}
                              </option>
                              {Array.isArray(PriceData) &&
                              PriceData.length > 0 ? (
                                PriceData.map((item, index) => (
                                  <option key={index} value={item.Price_CD}>
                                    {item.Price_CD}
                                  </option>
                                ))
                              ) : (
                                <option value="">No data found</option>
                              )}
                            </select>
                            <input
                              disabled
                              id="Unit_Price"
                              value={orderData?.Unit_Price ?? ""}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-14"
                            />
                          </div>
                        </div>
                        <div className="flex gap-2 pl-1">
                          <div className="flex gap-1">
                            <input
                              id="Temp_Shipment"
                              type="checkbox"
                              checked={planData?.Temp_Shipment}
                              onChange={handlePlanInputChange}
                              className="form-checkbox border-gray-400 rounded"
                            />
                            <label className="text-xs mt-1">
                              Temp_Shipment
                            </label>
                          </div>
                        </div>
                        <div className="flex gap-1 ml-1.5">
                          <label className="text-xs mt-1 mr-1.5">
                            Supple_Docu
                          </label>
                          <div className="flex gap-1">
                            <input
                              id="Supple_Docu"
                              value={orderData?.Supple_Docu ?? ""}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 w-[126px]"
                            />
                          </div>
                        </div>
                        <div className="flex gap-2.5 w-auto pl-8">
                          <label className="w-8 text-xs item">Target</label>
                          <div className="w-auto flex gap-1">
                            <select
                              id="Target_CD"
                              value={orderData?.Target_CD ?? ""}
                              onChange={handleInputChange}
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-xs bg-[#ffff99] w-20"
                            >
                              <option value={orderData?.Target_CD || ""}>
                                {orderData?.Target_CD || ""}
                              </option>
                              {Array.isArray(TargetData) &&
                              TargetData.length > 0 ? (
                                TargetData.map((item, index) => (
                                  <option key={index} value={item.Target_CD}>
                                    {item.Target_CD}
                                  </option>
                                ))
                              ) : (
                                <option value="">No data found</option>
                              )}
                            </select>
                            <input
                              disabled
                              value={targetName ?? ""}
                              onChange={(event) => setTargetData(event)}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-20"
                            />
                          </div>
                        </div>
                        <div className="flex gap-2.5 pl-4">
                          <label className="text-xs">Shipment</label>
                          <input
                            disabled
                            id="Shipment_Date"
                            value={formatDate(orderData?.Shipment_Date ?? "")}
                            onChange={handleInputChange}
                            type="text"
                            className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-[100px]"
                          />
                        </div>
                      </div>

                      {/* Group 5 */}
                      <div className="flex flex-row gap-2 pb-1 justify-start">
                        <div className="flex gap-2 pl-[21px]">
                          <label className="text-xs">Nav</label>
                          <input
                            id="NAV_Delivery"
                            value={orderData?.NAV_Delivery ?? ""}
                            onChange={handleInputChange}
                            type="text"
                            className="bg-[#ff99cc] border-solid border-2 border-gray-500 rounded-md px-1 w-28"
                            readOnly
                          />
                        </div>
                        <div className="flex gap-2 pl-5">
                          <label className="text-xs">Pd_Draw</label>
                          <input
                            disabled
                            id="Product_Draw"
                            value={orderData?.Product_Draw ?? ""}
                            onChange={handleInputChange}
                            type="text"
                            className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 w-40"
                          />
                        </div>
                        <div className="flex gap-2">
                          <label className="text-xs">Mate5</label>
                          <div className="flex gap-1">
                            <input
                              disabled
                              id="Material5"
                              value={orderData?.Material5 ?? ""}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-whtie border-solid border-2 border-gray-500 rounded-md px-1 w-20"
                            />
                            <input
                              disabled
                              id="H_Treatment5"
                              value={orderData?.H_Treatment5 ?? ""}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-whtie border-solid border-2 border-gray-500 rounded-md px-1 w-20"
                            />
                          </div>
                        </div>
                        <div className="flex gap-0.5">
                          <label className="text-xs">Supple</label>
                          <div className="flex">
                            <select
                              id="Supply_CD"
                              value={orderData?.Supply_CD ?? ""}
                              onChange={handleInputChange}
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-xs bg-[#ffff99] w-20"
                            >
                              <option value={orderData?.Supply_CD || ""}>
                                {orderData?.Supply_CD || ""}
                              </option>
                              {Array.isArray(SupplyData) &&
                              SupplyData.length > 0 ? (
                                SupplyData.map((item, index) => (
                                  <option key={index} value={item.Supply_CD}>
                                    {item.Supply_CD}
                                  </option>
                                ))
                              ) : (
                                <option value="">No data found</option>
                              )}
                            </select>
                          </div>
                        </div>
                        <div className="flex gap-2 pl-[42px]">
                          <div className="flex gap-1 ">
                            <input
                              id="Unreceived"
                              type="checkbox"
                              checked={planData?.Unreceived}
                              onChange={handlePlanInputChange}
                              className="form-checkbox border-gray-400 rounded"
                            />
                            <label className="text-xs mt-1">Unreceived</label>
                            <input
                              id="Od_CAT1"
                              type="checkbox"
                              checked={planData?.Od_CAT1}
                              onChange={handlePlanInputChange}
                              className="form-checkbox border-gray-400 rounded ml-2"
                            />
                            <label className="text-xs mt-1">Od_CAT1</label>
                            <input
                              id="Od_CAT2"
                              type="checkbox"
                              checked={planData?.Od_CAT2}
                              onChange={handlePlanInputChange}
                              className="form-checkbox border-gray-400 rounded ml-2"
                            />
                            <label className="text-xs mt-1">Od_CAT2</label>
                            <input
                              id="Od_CAT3"
                              type="checkbox"
                              checked={planData?.Od_CAT3}
                              onChange={handlePlanInputChange}
                              className="form-checkbox border-gray-400 rounded ml-2"
                            />
                            <label className="text-xs mt-1">Od_CAT3</label>
                          </div>
                        </div>

                        <div className="flex gap-1.5 pl-1">
                          <label className="text-xs mt-1">Pd_Target_Qty</label>
                          <div className="flex gap-1">
                            <input
                              id="Pd_Target_Qty"
                              value={orderData?.Pd_Target_Qty ?? ""}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 w-[168px]"
                            />
                          </div>
                        </div>
                        <div className="flex gap-1">
                          <label className="text-xs">Calc_Process</label>
                          <input
                            disabled
                            id="Calc_Process_Date"
                            value={formatDate(
                              orderData?.Calc_Process_Date ?? ""
                            )}
                            onChange={handleInputChange}
                            type="text"
                            className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-[100px]"
                          />
                        </div>
                      </div>
                    </div>
                  )}
                </div>
                {/* End Order Form */}

                <hr
                  className="border-[1px] border-gray-300 w-full xl:mt-3"
                  style={{
                    transform: `translateY(${translateYPlan}px)`,
                    transition: "transform 0.3s ease",
                  }}
                />

                {/* Start Plan Form */}
                <div
                  style={{
                    transform: `translateY(${translateYPlan}px)`,
                    transition: "transform 0.3s ease",
                  }}
                >
                  <div
                    className="pl-5"
                    style={{
                      transform: `scale(${scalePlaninfo})`,
                      transformOrigin: "top left",
                      transition: "transform 0.3s ease",
                    }}
                  >
                    {/* Head Plan_info */}
                    <div className="flex items-center font-medium my-2">
                      <label className="text-lg">[Plan_info]</label>

                      <button
                        onClick={togglePlanForm}
                        className="ml-5 p-1 border border-gray-400 rounded hover:bg-gray-100 transition"
                      >
                        {showPlanForm ? (
                          <FaEyeSlash size={20} />
                        ) : (
                          <FaEye size={20} />
                        )}
                      </button>
                    </div>
                    {showPlanForm && (
                      <div>
                        {/* Group 1 */}
                        <div className="flex flex-row gap-2 pb-1 justify-start">
                          <div className="flex gap-1">
                            <label className="text-xs">Parts_No</label>
                            <input
                              ref={PartsNo}
                              disabled
                              id="Parts_No"
                              value={planData?.Parts_No || ""}
                              onChange={handlePlanInputChange}
                              type="text"
                              className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 w-20"
                            />
                          </div>
                          <div className="flex gap-1 pl-2">
                            <label className="text-xs">Parts_Delivery</label>
                            <div className="relative">
                              <DatePicker
                                key={dates.planDeliDate || " "}
                                selected={dates.planDeliDate}
                                onChange={(date) =>
                                  handleDateChange("planDeliDate", date)
                                }
                                dateFormat="dd/MM/yyyy"
                                // showTimeSelect
                                // timeFormat="HH:mm"
                                // timeIntervals={15}
                                ref={planDeliDateRef}
                                isClearable
                                className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 max-w-[125px] cursor-pointer"
                                popperPlacement="bottom-start"
                                portalId="root-portal"
                                inline={false} // ตั้งค่า inline ให้ false
                                disabled={isDatePickerDisabled}
                              />
                              {!dates.planDeliDate && (
                                <AiTwotoneCalendar
                                  className="absolute right-2 top-1.5 text-gray-500 cursor-pointer"
                                  size={16}
                                  onClick={() =>
                                    handleOpenDatePicker(planDeliDateRef)
                                  } // เปิด DatePicker เมื่อคลิก
                                />
                              )}
                            </div>
                          </div>
                          <div className="flex gap-2 pl-16">
                            <label className="text-xs">[Parts_List]</label>
                          </div>
                          <div className="flex gap-2 pl-2">
                            <label className="text-xs">RegPerson</label>
                            <div className="flex gap-1">
                              <Select
                                ref={RegPerson}
                                inputId="Pl_Reg_Person_CD"
                                value={
                                  planData?.Pl_Reg_Person_CD
                                    ? {
                                        label: planData.Pl_Reg_Person_CD,
                                        value: planData.Pl_Reg_Person_CD,
                                      }
                                    : null
                                }
                                onChange={(selectedOption) =>
                                  handlePlanInputChange(
                                    null,
                                    selectedOption,
                                    "Pl_Reg_Person_CD"
                                  )
                                }
                                options={
                                  Array.isArray(WorkerData)
                                    ? WorkerData.map((item) => ({
                                        label: item.Worker_CD,
                                        value: item.Worker_CD,
                                      }))
                                    : []
                                }
                                className="text-xs text-left w-24"
                                noOptionsMessage={() => "No data found"}
                                isDisabled={!isPlanDateEditable}
                                styles={{
                                  control: (base) => ({
                                    ...base,
                                    backgroundColor: "#ffff99",
                                    borderColor: "#6b7280", // เทา (tailwind: gray-500)
                                    minHeight: "32px",
                                    height: "32px",
                                  }),
                                  valueContainer: (base) => ({
                                    ...base,
                                    padding: "0 8px",
                                  }),
                                  indicatorsContainer: (base) => ({
                                    ...base,
                                    height: "32px",
                                  }),
                                  input: (base) => ({
                                    ...base,
                                    margin: 0,
                                    padding: 0,
                                  }),
                                }}
                              />
                              <input
                                disabled
                                id="Pl_Reg_Person_Name"
                                value={Person_Name || ""}
                                onChange={(event) => setWorkerData(event)}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-20"
                              />
                              <label className="w-32 text-xs pl-2 pt-1">
                                Company Information
                              </label>
                              <label className="w-24 text-xs pt-1">
                                Note/Remark/Info
                              </label>
                            </div>
                          </div>
                          <div className="flex gap-2 -mt-1">
                            <div className="flex gap-1 ">
                              <input
                                disabled
                                id="Pt_CAT1"
                                checked={planData?.Pt_CAT1}
                                onChange={handlePlanInputChange}
                                type="checkbox"
                                className="form-checkbox border-gray-400 rounded ml-3"
                              />
                              <label className="text-xs mt-2">Pt_CAT1</label>
                              <input
                                disabled
                                id="Pt_CAT2"
                                checked={planData?.Pt_CAT2}
                                onChange={handlePlanInputChange}
                                type="checkbox"
                                className="form-checkbox border-gray-400 rounded ml-3"
                              />
                              <label className="text-xs mt-2">Pt_CAT2</label>
                              <input
                                disabled
                                id="Pt_CAT3"
                                checked={planData?.Pt_CAT3}
                                onChange={handlePlanInputChange}
                                type="checkbox"
                                className="form-checkbox border-gray-400 rounded ml-3"
                              />
                              <label className="text-xs mt-2">Pt_CAT3</label>
                            </div>
                          </div>

                          <div className="flex gap-2 ml-4">
                            <label className="text-xs item">Progress</label>
                            <div className="flex gap-1">
                              <select
                                disabled
                                id="Pl_Progress_CD"
                                value={planData?.Pl_Progress_CD || ""}
                                onChange={handlePlanInputChange}
                                className="border-2 border-gray-500 rounded-md px-2 py-1 text-xs bg-[#ffff99] w-20"
                              >
                                <option value={planData?.Pl_Progress_CD || ""}>
                                  {planData?.Pl_Progress_CD || ""}
                                </option>
                                {Array.isArray(plprogressData) &&
                                plprogressData.length > 0 ? (
                                  plprogressData.map((item, index) => (
                                    <option
                                      key={index}
                                      value={item.Pl_Progress_CD}
                                    >
                                      {item.Pl_Progress_CD}
                                    </option>
                                  ))
                                ) : (
                                  <option value="">No data found</option>
                                )}
                              </select>
                              <input
                                disabled
                                type="text"
                                id="Pl_Progress_Name"
                                value={ProgressName || ""}
                                onChange={(event) => setPlProgressData(event)}
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-20"
                              />
                            </div>
                          </div>
                        </div>

                        {/* Group 2 */}
                        <div className="flex flex-row gap-2 pb-1 justify-start">
                          <div className="flex flex-col gap-4">
                            <div className="flex gap-1">
                              <label className="text-xs">Pt_Name</label>
                              <Select
                                inputId="Parts_CD"
                                value={
                                  planData?.Parts_CD
                                    ? {
                                        label: Array.isArray(PartsData)
                                          ? PartsData.find(
                                              (item) =>
                                                item.Parts_CD ===
                                                planData.Parts_CD
                                            )?.Parts_Abb || ""
                                          : "",
                                        value: planData.Parts_CD,
                                      }
                                    : null
                                }
                                onChange={(selectedOption) =>
                                  handlePlanInputChange(
                                    null,
                                    selectedOption,
                                    "Parts_CD"
                                  )
                                }
                                options={[
                                  ...(isF3Pressed
                                    ? [{ label: "Base", value: "" }] // เพิ่ม option Base เมื่อ F3 ถูกกด
                                    : []),
                                  ...(Array.isArray(PartsData)
                                    ? PartsData.map((item) => ({
                                        label: item.Parts_Abb,
                                        value: item.Parts_CD,
                                      }))
                                    : []),
                                ]}
                                className="text-xs text-left w-24"
                                noOptionsMessage={() => "No data found"}
                                isDisabled={!isPlanDateEditable} // แทน disabled
                                styles={{
                                  control: (base) => ({
                                    ...base,
                                    backgroundColor: "#ffff99",
                                    borderColor: "#6b7280", // gray-500
                                    minHeight: "32px",
                                    height: "32px",
                                  }),
                                  valueContainer: (base) => ({
                                    ...base,
                                    padding: "0 8px",
                                  }),
                                  indicatorsContainer: (base) => ({
                                    ...base,
                                    height: "32px",
                                  }),
                                  input: (base) => ({
                                    ...base,
                                    margin: 0,
                                    padding: 0,
                                  }),
                                }}
                              />

                              <label className="text-xs pl-10">Pt_Mate</label>
                              <div className="w-auto">
                                <input
                                  disabled
                                  id="Pt_Material"
                                  value={planData?.Pt_Material || ""}
                                  onChange={handlePlanInputChange}
                                  type="text"
                                  className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 w-32"
                                />
                              </div>
                            </div>

                            <div className="flex gap-2 -mt-3 pl-2">
                              <label className="text-xs">Pt_Qty</label>
                              <div className="flex gap-1">
                                <input
                                  disabled
                                  id="Pt_Qty"
                                  value={
                                    isF3Pressed
                                      ? orderData?.Quantity ?? ""
                                      : planData?.Pt_Qty ?? ""
                                  }
                                  onChange={handlePlanInputChange}
                                  type="text"
                                  className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 w-20"
                                />

                                <select
                                  disabled
                                  id="Pt_Unit_CD"
                                  value={
                                    isF3Pressed
                                      ? orderData?.Unit_CD || "" // แสดงค่า orderData?.Unit_CD เมื่อกด F3
                                      : planData?.Pt_Unit_CD ?? "" // แสดงค่า planData?.Pt_Unit_CD ก่อนกด F3
                                  }
                                  onChange={handlePlanInputChange}
                                  className="border-2 border-gray-500 rounded-md px-2 py-1 text-xs bg-[#ffff99] w-20"
                                >
                                  <option value={planData?.Pt_Unit_CD || ""}>
                                    {UnitName}
                                  </option>
                                  {Array.isArray(UnitsData) &&
                                  UnitsData.length > 0 ? (
                                    UnitsData.map((item, index) => (
                                      <option key={index} value={item.Unit_CD}>
                                        {item.Unit_Abb}
                                      </option>
                                    ))
                                  ) : (
                                    <option value="">No data found</option>
                                  )}
                                </select>
                              </div>
                              <input
                                disabled
                                id="Pt_Split"
                                checked={planData?.Pt_Split}
                                onChange={handlePlanInputChange}
                                type="checkbox"
                                className="form-checkbox border-gray-400 rounded ml-2"
                              />
                              <label className="text-xs mt-1">Split</label>
                              <label className="text-xs">Sp_Qty</label>
                              <input
                                disabled
                                id="Pt_Spare_Qty"
                                value={planData?.Pt_Spare_Qty ?? ""}
                                onChange={handlePlanInputChange}
                                type="text"
                                className={`border-solid border-2 border-gray-500 rounded-md px-1 w-20 
                                ${
                                  planData?.Pt_Spare_Qty > 0
                                    ? "bg-blue-300"
                                    : "bg-[#ffff99]"
                                }`}
                              />
                            </div>

                            <div className="flex gap-2 -mt-3">
                              <label className="text-xs">Connect_Od_No</label>
                              <div className="w-auto">
                                <div
                                  onDoubleClick={() => {
                                    handlePlanInputChange({
                                      target: {
                                        id: "Connect_Od_No",
                                        value: planData?.Order_No ?? "",
                                      },
                                    });
                                  }}
                                >
                                  <input
                                    disabled
                                    id="Connect_Od_No"
                                    value={planData?.Connect_Od_No ?? ""}
                                    onChange={handlePlanInputChange}
                                    type="text"
                                    className="bg-[#ccffcc] border-solid border-2 border-gray-500 rounded-md px-1 w-[120px]"
                                  />
                                </div>
                              </div>
                              <label className="text-xs pl-14">NG_Qty</label>
                              <div className="flex gap-1">
                                <input
                                  disabled
                                  id="Pt_NG_Qty"
                                  value={planData?.Pt_NG_Qty ?? ""}
                                  onChange={handlePlanInputChange}
                                  type="text"
                                  className={`bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 w-20 
                                  ${
                                    planData?.Pt_NG_Qty > 0
                                      ? "bg-pink-300"
                                      : "bg-[#ffff99]"
                                  }`}
                                />
                              </div>
                            </div>

                            <div className="flex gap-2 -mt-3">
                              <label className="text-xs">Connect_Pt_No</label>
                              <div className="w-auto flex gap-1 pl-1.5">
                                <input
                                  disabled
                                  id="Connect_Pt_No"
                                  value={planData?.Connect_Pt_No ?? ""}
                                  onChange={handlePlanInputChange}
                                  type="text"
                                  className="bg-[#ccffcc] border-solid border-2 border-gray-500 rounded-md px-1 w-14"
                                />
                                <input
                                  disabled
                                  id="Connect_Pt_Abb"
                                  value={Connect_Pt_Abb}
                                  onChange={handlePlanInputChange}
                                  type="text"
                                  className="bg-[#FFFFFF] border-solid border-2 border-gray-500 rounded-md px-1 w-[58px]"
                                />
                              </div>
                              <input
                                disabled
                                id="Pt_Pending"
                                checked={planData?.Pt_Pending}
                                onChange={handlePlanInputChange}
                                type="checkbox"
                                className="form-checkbox border-gray-400 rounded ml-2"
                              />
                              <label className="text-xs mt-1">Pt_pend</label>
                            </div>

                            <div className="flex gap-2 -mt-3">
                              <label className="text-xs">Connect_Pr_No</label>
                              <div className="flex gap-1 pl-1.5">
                                <input
                                  disabled
                                  id="Connect_Pr_No"
                                  value={planData?.Connect_Pr_No ?? ""}
                                  onChange={handlePlanInputChange}
                                  type="text"
                                  className="bg-[#ccffcc] border-solid border-2 border-gray-500 rounded-md px-1 w-14"
                                />
                                <input
                                  disabled
                                  id="Connect_Pr_Abb"
                                  value={Connect_Pr_Abb}
                                  onChange={handlePlanInputChange}
                                  type="text"
                                  className="bg-[#FFFFFF] border-solid border-2 border-gray-500 rounded-md w-[58px]"
                                />
                              </div>
                              <input
                                disabled
                                id="Outside"
                                checked={planData?.Outside}
                                onChange={handlePlanInputChange}
                                type="checkbox"
                                className="form-checkbox border-gray-400 rounded ml-2"
                              />
                              <label className="text-xs mt-1">Outside</label>
                            </div>
                          </div>

                          <div className="flex gap-2 w-auto">
                            <div className="w-auto flex gap-1">
                              <div className="w-[360px] h-[200px] border border-gray-300 overflow-scroll">
                                <table className="border-separate border-spacing-0 text-xs">
                                  <thead className="bg-gray-100 overflow-scroll">
                                    <tr>
                                      <th className="border border-gray-300 py-2 px-5 text-center sticky top-0 left-0 bg-gray-100">
                                        P.
                                      </th>
                                      <th className="border border-gray-300 py-2 px-5 text-center sticky top-0 left-[51px] bg-gray-100">
                                        PI.
                                      </th>
                                      <th className="border border-gray-300 py-2 px-5 text-center sticky top-0 left-[107px] bg-gray-100">
                                        Part
                                      </th>
                                      <th className="border border-gray-300 py-2 px-5 text-center">
                                        Pt_Delivery
                                      </th>
                                      <th className="border border-gray-300 py-2 px-5 text-center">
                                        Pt_Material
                                      </th>
                                      <th className="border border-gray-300 py-2 px-5 text-center">
                                        Pt_Qty
                                      </th>
                                      <th className="border border-gray-300 py-2 px-5 text-center">
                                        Pt_Spare_Qty
                                      </th>
                                      <th className="border border-gray-300 py-2 px-5 text-center">
                                        Pt_NG_Qty
                                      </th>
                                      <th className="border border-gray-300 py-2 px-5 text-center">
                                        Pt_Instructions
                                      </th>
                                      <th className="border border-gray-300 py-2 px-5 text-center">
                                        Pt_Remark
                                      </th>
                                      <th className="border border-gray-300 py-2 px-5 text-center">
                                        Pt_Information
                                      </th>
                                      <th className="border border-gray-300 py-2 px-5 text-center">
                                        Connect_Od_No
                                      </th>
                                      <th className="border border-gray-300 py-2 px-5 text-center">
                                        Connect_Pt_No
                                      </th>
                                      <th className="border border-gray-300 py-2 px-5 text-center">
                                        Connect_Pr_No
                                      </th>
                                      <th className="border border-gray-300 py-2 px-5 text-center">
                                        Pl_Ed_Rev_Day
                                      </th>
                                      <th className="border border-gray-300 py-2 px-5 text-center">
                                        Pl_Schedule_CD
                                      </th>
                                      <th className="border border-gray-300 py-2 px-5 text-center">
                                        Pl_Reg_Date
                                      </th>
                                      <th className="border border-gray-300 py-2 px-5 text-center">
                                        Pt_Complete_Date
                                      </th>
                                      <th className="border border-gray-300 py-2 px-5 text-center">
                                        Pl_Upd_Date
                                      </th>
                                      {Array.from(
                                        { length: 36 },
                                        (_, index) => (
                                          <th
                                            key={`ppc-th${index + 1}`}
                                            className="border border-gray-300 py-2 px-5 text-center"
                                          >
                                            PPC{index + 1}
                                          </th>
                                        )
                                      )}
                                    </tr>
                                  </thead>
                                  <tbody>
                                    {Array.isArray(selectedPlanNo) &&
                                    selectedPlanNo.length > 0 ? (
                                      selectedPlanNo.map((item, index) => (
                                        <tr key={index}>
                                          <td className="border border-gray-300 h-8 sticky top-0 left-0 bg-white">
                                            {item.Parts_No}
                                          </td>
                                          <td className="border border-gray-300 h-8 sticky top-0 left-[51px] bg-white">
                                            {item.Pl_Progress_CD}
                                          </td>
                                          <td className="border border-gray-300 h-8 sticky top-0 left-[107px] bg-white">
                                            {item.Parts_No}
                                          </td>
                                          <td className="border border-gray-300 h-8">
                                            {new Date(
                                              item.Pt_Delivery
                                            ).toLocaleDateString("th-TH", {
                                              day: "2-digit",
                                              month: "2-digit",
                                              year: "numeric",
                                            })}
                                          </td>
                                          <td className="border border-gray-300 h-8">
                                            {item.Pt_Material}
                                          </td>
                                          <td className="border border-gray-300 h-8">
                                            {item.Pt_Qty}
                                          </td>
                                          <td className="border border-gray-300 h-8">
                                            {item.Pt_Spare_Qty}
                                          </td>
                                          <td className="border border-gray-300 h-8">
                                            {item.Pt_NG_Qty}
                                          </td>
                                          <td className="border border-gray-300 h-8">
                                            {item.Pt_Instructions}
                                          </td>
                                          <td className="border border-gray-300 h-8">
                                            {item.Pt_Remark}
                                          </td>
                                          <td className="border border-gray-300 h-8">
                                            {item.Pt_Information}
                                          </td>
                                          <td className="border border-gray-300 h-8">
                                            {item.Connect_Od_No}
                                          </td>
                                          <td className="border border-gray-300 h-8">
                                            {item.Connect_Pt_No}
                                          </td>
                                          <td className="border border-gray-300 h-8">
                                            {item.Connect_Pr_No}
                                          </td>
                                          <td className="border border-gray-300 h-8">
                                            {item.Pl_Ed_Rev_Day}
                                          </td>
                                          <td className="border border-gray-300 h-8">
                                            {item.Pl_Schedule_CD}
                                          </td>

                                          <td className="border border-gray-300 h-8">
                                            {new Date(
                                              item.Pl_Reg_Date
                                            ).toLocaleDateString("th-TH", {
                                              day: "2-digit",
                                              month: "2-digit",
                                              year: "numeric",
                                            })}
                                          </td>
                                          <td className="border border-gray-300 h-8">
                                            {new Date(
                                              item.Pt_Complete_Date
                                            ).toLocaleDateString("th-TH", {
                                              day: "2-digit",
                                              month: "2-digit",
                                              year: "numeric",
                                            })}
                                          </td>
                                          <td className="border border-gray-300 h-8">
                                            {new Date(
                                              item.Pl_Upd_Date
                                            ).toLocaleDateString("th-TH", {
                                              day: "2-digit",
                                              month: "2-digit",
                                              year: "numeric",
                                            })}
                                          </td>
                                          {Array.from(
                                            { length: 36 },
                                            (_, index) => (
                                              <td
                                                key={`ppc-td${index + 1}`}
                                                className="border border-gray-300 h-8"
                                              >
                                                {item[`PPC${index + 1}`]}
                                              </td>
                                            )
                                          )}
                                        </tr>
                                      ))
                                    ) : (
                                      <tr>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                        <td className="border border-gray-300 h-8"></td>
                                      </tr>
                                    )}
                                  </tbody>
                                </table>
                              </div>
                            </div>
                          </div>

                          <div className="flex gap-2">
                            <div className="w-auto flex flex-col gap-2">
                              {[1, 2, 3, 4, 5, 6].map((info) => (
                                <div className="flex items-center" key={info}>
                                  <label className="text-xs mr-2">{info}</label>
                                  <select
                                    disabled
                                    id={`Info${info}`}
                                    value={planData?.[`Info${info}`] || ""}
                                    onChange={handlePlanInputChange}
                                    className="border-2 border-gray-500 rounded-md px-2 py-1 text-xs w-24"
                                  >
                                    <option value=""></option>
                                    {Array.isArray(processData) &&
                                    processData.length > 0 ? (
                                      processData.map((item, index) => (
                                        <option
                                          key={index}
                                          value={item.Process_CD}
                                        >
                                          {item.Process_Abb}
                                        </option>
                                      ))
                                    ) : (
                                      <option value="">No data found</option>
                                    )}
                                  </select>
                                </div>
                              ))}
                            </div>
                          </div>

                          <div className="flex gap-2 h-10">
                            <div className="w-16 flex flex-col gap-2">
                              {[1, 2, 3, 4, 5, 6].map((chk) => {
                                const id = `Info_Chk${chk}`;
                                return (
                                  <div
                                    className="flex items-center h-full py-1.5"
                                    key={chk}
                                  >
                                    <label
                                      className="text-xs mr-2"
                                      htmlFor={id}
                                    >
                                      Chk {chk}
                                    </label>
                                    <input
                                      id={id}
                                      checked={planData?.[id] || false} // อ่านค่าจาก checkboxStates
                                      onChange={handlePlanInputChange} // ใช้ฟังก์ชัน handleCheckboxChange
                                      type="checkbox"
                                      className="form-checkbox border-gray-400 rounded ml-2"
                                    />
                                  </div>
                                );
                              })}
                            </div>
                          </div>
                          <div className="flex gap-2 w-auto ">
                            <div className="w-auto">
                              <table className="w-[400px] text-xs border border-gray-300">
                                <tbody>
                                  <tr>
                                    <td className="border border-gray-300 h-44 w-1/2">
                                      <textarea
                                        id="Pt_Instructions"
                                        type="textarea"
                                        value={planData?.Pt_Instructions || ""}
                                        onChange={handlePlanInputChange}
                                        className="w-full h-full border-none focus:outline-none text-left align-top resize-none"
                                        placeholder="Enter text here"
                                      ></textarea>
                                    </td>
                                    <td className="border border-gray-300 h-24 w-1/2">
                                      <textarea
                                        id="Pt_Remark"
                                        type="textarea"
                                        value={planData?.Pt_Remark || ""}
                                        onChange={handlePlanInputChange}
                                        className="w-full h-full border-none focus:outline-none text-left align-top resize-none"
                                        placeholder="Enter text here"
                                      ></textarea>
                                    </td>
                                  </tr>
                                  <tr>
                                    <td
                                      className="border border-gray-300 h-8"
                                      colSpan="2"
                                    >
                                      <textarea
                                        id="Pt_Information"
                                        type="textarea"
                                        value={planData?.Pt_Information || ""}
                                        onChange={handlePlanInputChange}
                                        className="w-full h-full border-none focus:outline-none text-left align-top resize-none"
                                        placeholder="Enter text here"
                                      ></textarea>
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                            </div>
                          </div>

                          <div className="flex flex-col gap-2">
                            <div className="flex gap-1">
                              <label className="text-xs">Plan_Reg</label>
                              <input
                                disabled
                                id="Pl_Reg_Date"
                                value={
                                  planData?.Pl_Reg_Date
                                    ? planData.Pl_Reg_Date.substring(0, 16) // แสดงเป็น YYYY-MM-DDTHH:MM
                                    : ""
                                }
                                onChange={handlePlanInputChange}
                                type="datetime-local"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md pl-1 w-[190px]"
                              />
                            </div>
                            <div className="flex gap-1">
                              <label className="text-xs">Pt_Comp</label>
                              <input
                                disabled
                                id="Pt_Complete_Date"
                                value={
                                  planData?.Pt_Complete_Date
                                    ? new Date(planData.Pt_Complete_Date)
                                        .toISOString()
                                        .split("T")[0]
                                    : null
                                }
                                onChange={handlePlanInputChange}
                                type="date"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md pl-1 w-[190px]"
                              />
                            </div>

                            <div className="flex gap-1 -ml-1">
                              <label className="w-auto text-xs">QC_Comp</label>
                              <div className="w-auto">
                                <input
                                  disabled
                                  id="Pt_I_Date"
                                  value={
                                    planData?.Pt_I_Date
                                      ? new Date(planData.Pt_I_Date)
                                          .toISOString()
                                          .split("T")[0]
                                      : ""
                                  }
                                  onChange={handlePlanInputChange}
                                  type="date"
                                  className="bg-white border-solid border-2 border-gray-500 rounded-md pl-1 w-[190px]"
                                />
                              </div>
                            </div>

                            <div className="flex gap-1">
                              <label className="text-xs mt-1">Plan_Upd</label>

                              <input
                                disabled
                                id="Pl_Upd_Date"
                                value={
                                  planData?.Pl_Upd_Date
                                    ? planData.Pl_Upd_Date.substring(0, 16)
                                    : ""
                                }
                                onChange={handlePlanInputChange}
                                type="datetime-local"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md pl-1 w-[190px]"
                              />
                            </div>

                            <div className="flex gap-1 -ml-2">
                              <label className="text-xs">UpdPerson</label>
                              <div className="flex flex-row gap-1">
                                <select
                                  disabled
                                  id="Pl_Upd_Person_CD"
                                  value={planData?.Pl_Upd_Person_CD}
                                  onChange={handlePlanInputChange}
                                  className="border-2 border-gray-500 rounded-md px-2 py-1 text-xs bg-[#ffff99] w-20"
                                >
                                  <option value={planData?.Pl_Upd_Person_CD}>
                                    {planData?.Pl_Upd_Person_CD}
                                  </option>
                                  {Array.isArray(WorkerData) &&
                                  WorkerData.length > 0 ? (
                                    WorkerData.map((item, index) => (
                                      <option
                                        key={index}
                                        value={item.Worker_CD}
                                      >
                                        {item.Worker_CD}
                                      </option>
                                    ))
                                  ) : (
                                    <option value="">No data found</option>
                                  )}
                                </select>
                                <input
                                  disabled
                                  id="Pl_Upd_Person_Name"
                                  value={updPerson_Name || ""}
                                  onChange={(event) => setWorkerData(event)}
                                  type="text"
                                  className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 pl-1 w-20"
                                />
                              </div>
                            </div>

                            <div className="flex gap-1">
                              <label className="text-xs">Schedule</label>
                              <div className="flex gap-1">
                                <select
                                  disabled
                                  id="Pl_Schedule_CD"
                                  value={planData?.Pl_Schedule_CD || ""}
                                  onChange={handlePlanInputChange}
                                  className="border-2 border-gray-500 rounded-md px-2 py-1 text-xs bg-[#ffff99] w-20"
                                >
                                  <option
                                    value={planData?.Pl_Schedule_CD || ""}
                                  >
                                    {planData?.Pl_Schedule_CD || ""}
                                  </option>
                                  {Array.isArray(ScheduleData) &&
                                  ScheduleData.length > 0 ? (
                                    ScheduleData.map((item, index) => (
                                      <option
                                        key={index}
                                        value={item.Schedule_CD}
                                      >
                                        {item.Schedule_CD}
                                      </option>
                                    ))
                                  ) : (
                                    <option value="">No data found</option>
                                  )}
                                </select>
                                <input
                                  disabled
                                  id="Pl_Schedule_Name"
                                  value={Schedule_Name || ""}
                                  onChange={(event) => setScheduleData(event)}
                                  type="text"
                                  className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-20"
                                />
                              </div>
                            </div>

                            <div className="flex gap-1 -ml-[74px]">
                              <label className="text-xs">
                                Starnat|ManHour_Scale
                              </label>
                              <div className="flex gap-1">
                                <input
                                  disabled
                                  id="Stagnat_Scale"
                                  value={Stagnat_Scale || ""}
                                  onChange={(event) => setScheduleData(event)}
                                  type="text"
                                  className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-20"
                                />
                                <input
                                  disabled
                                  id="ManHour_Scale"
                                  value={ManHour_Scale || ""}
                                  onChange={(event) => setScheduleData(event)}
                                  type="text"
                                  className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-20"
                                />
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Group 3 */}
                        <div className="gap-2 flex justify-start pl-16 -mt-6 mb-3">
                          <div className="flex items-center gap-2">
                            <label className="text-xs">PI_Quote_OdPt_No</label>
                            <div className="w-auto">
                              <input
                                disabled
                                id="Pl_Quote_OdPt_No"
                                value={planData?.Pl_Quote_OdPt_No || ""}
                                onChange={handlePlanInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-40 ml-1"
                              />
                              <input
                                disabled
                                id="Pl_Quote_CD"
                                value={planData?.Pl_Quote_CD || ""}
                                onChange={handlePlanInputChange}
                                type="hidden"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-40 ml-1"
                              />
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <label className="w-10  text-xs">PI_Quote</label>
                            <div className="w-auto">
                              <input
                                disabled
                                id="PI_Quote"
                                value={Pl_Quote_Abb}
                                onChange={handlePlanInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-40 ml-1"
                              />
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <label className="w-20 text-xs">
                              Start_Rev_Days
                            </label>
                            <div className="w-auto flex gap-1">
                              <input
                                disabled
                                id="Pl_St_Rev_Day"
                                value={planData?.Pl_St_Rev_Day ?? ""}
                                onChange={handlePlanInputChange}
                                type="text"
                                className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 w-28"
                              />
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <label className="w-20 text-xs">End_Rev_Days</label>
                            <div className="w-auto flex gap-1">
                              <input
                                disabled
                                id="Pl_Ed_Rev_Day"
                                value={planData?.Pl_Ed_Rev_Day ?? ""}
                                onChange={handlePlanInputChange}
                                type="text"
                                className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 w-28"
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
                {/* End Plan Form */}
              </div>
            </div>

            <div
              className="w-full px-2"
              style={{
                transform: `translateY(${translateYFactor}px)`,
                transformOrigin: "top center",
                marginBottom: `${marginBottom}px`,
                transition: "transform 0.3s ease",
              }}
            >
              <div className="flex gap-4 flex-wrap text-[12px] p-2 bg-gray-100 border border-gray-300">
                <label className="flex items-center gap-1 font-bold text-blue-700">
                  <input
                    type="checkbox"
                    checked={isAllSelected}
                    onChange={toggleAllColumns}
                  />
                  Select All
                </label>

                {allColumns.map((key) => (
                  <label key={key} className="flex items-center gap-1">
                    <input
                      type="checkbox"
                      checked={columnVisibility[key]}
                      onChange={() => toggleColumn(key)}
                    />
                    {key}
                  </label>
                ))}

                <div className="flex gap-1.5 items-center">
                  <label className="text-xs">In_Inst(TooPcNo)</label>
                  <div className="flex gap-1">
                    <button
                      disabled={!buttonState.ScheduleCalc}
                      onClick={Schedule_Calc_Click}
                      className={`py-1 px-2 rounded text-xs ${
                        buttonState.ScheduleCalc
                          ? "bg-gray-300"
                          : "bg-gray-300 opacity-50"
                      }`}
                    >
                      ScheduleCalc
                    </button>
                  </div>
                </div>
                <div className="flex gap-1.5 items-center">
                  <label className="wtext-xs">Type</label>
                  <div className="flex gap-1">
                    <select
                      id="Sc_Make_Type"
                      value={planData?.Sc_Make_Type || ""}
                      onChange={handlePlanInputChange}
                      className="border-2 border-gray-500 rounded-md px-2 py-1 text-xs bg-[#ffff99] w-24"
                    >
                      <option value=""></option>
                      <option value="Forward">Forward</option>
                      <option value="Equality">Equality</option>
                      <option value="Backward">Backward</option>
                    </select>
                  </div>
                </div>
                <div className="flex gap-1.5 items-center">
                  <input
                    disabled
                    id="Money_Object"
                    checked={planData?.Money_Object}
                    onChange={handleMoneyObjectChange}
                    type="checkbox"
                    className="form-checkbox border-gray-400 rounded"
                  />
                  <label className="text-xs">Money_Object</label>

                  <div className="flex gap-1">
                    <input
                      disabled
                      id="Amount"
                      value={planData?.Amount}
                      onChange={handlePlanInputChange}
                      type="text"
                      className="bg-white border-solid border-2 border-gray-500 rounded-md p-1 w-24"
                    />
                  </div>
                </div>
              </div>

              {/* Table */}
              <div
                className="overflow-x-auto overflow-y-auto"
                 style={{ height: 'auto' }}
              >
                <table className="min-w-max border border-black">
                  <thead className="sticky top-0 z-20 bg-gray-300">
                    <tr className="text-black font-bold text-[10px]">
                      {!columnVisibility.Quoteinfo && (
                        <th
                          rowSpan="2"
                          className="sticky top-0 left-0 z-30 py-1 px-1 w-auto bg-gray-300"
                        >
                          No.
                        </th>
                      )}

                      {columnVisibility.Quoteinfo && (
                        <th
                          colSpan="2"
                          className="py-1 px-2 text-center bg-gray-100 "
                        >
                          <button
                            id="Quoteinfo"
                            disabled={!buttonState.Quoteinfo}
                            onClick={Quote_Info_View_Click}
                            className={`py-1 px-2 rounded ml-5 ${
                              buttonState.Quoteinfo
                                ? "bg-gray-300"
                                : "bg-gray-300 opacity-50"
                            }`}
                          >
                            Quote_Info_View
                          </button>
                        </th>
                      )}
                      {columnVisibility.Quoteinfo && (
                        <th
                          className="w-auto bg-gray-100 border border-gray-400 "
                          rowSpan="2"
                        >
                          <button
                            disabled={!buttonState.Quoteinfo}
                            onClick={() => quoteAllClick("all")} // กดปุ่ม "All"
                            className={`py-1 rounded h-10 
                                    ${
                                      buttonState.Quoteinfo
                                        ? "text-gray-900"
                                        : "text-gray-500"
                                    }`}
                          >
                            All ➔
                          </button>
                        </th>
                      )}
                      <th className="py-3 w-auto" rowSpan="2">
                        Plan_Process
                      </th>
                      <th className="py-1 px-1 w-auto" rowSpan="2">
                        Plan Machine(min)
                      </th>
                      <th className="py-1 px-3 min-w-[90px]" rowSpan="2">
                        Plan PS | <br /> Out min | (D)
                      </th>
                      <th className="py-1 w-auto" rowSpan="2">
                        Time/Proce/Sche
                      </th>
                      <th className="py-1 w-auto" rowSpan="2">
                        Plan_Date
                      </th>
                      <th className="py-1 w-auto" rowSpan="2">
                        Instructions
                      </th>
                      <th className="py-1 w-auto" rowSpan="2">
                        Result_Date
                      </th>
                      <th className="py-1 w-auto" rowSpan="2">
                        Result Machine <br /> (min/Lot)
                      </th>
                      <th className="py-1 w-auto" rowSpan="2">
                        Result Person <br /> (min/Lot)
                      </th>
                      <th className="py-1 w-auto" rowSpan="2">
                        Result Qty
                      </th>
                      {columnVisibility.ASP_Schedule && (
                        <th className="py-1 w-auto bg-gray-100 " rowSpan="2">
                          <button className=" py-1 px-2 rounded text-gray-500">
                            ASP Schedule Reflect
                          </button>
                        </th>
                      )}
                      {columnVisibility.Schedule_BK_RE && (
                        <th
                          className="py-1 px-2 w-auto bg-gray-100"
                          rowSpan="2"
                        >
                          <div className="flex flex-col gap-1">
                            <button
                              onClick={Schedule_BK_Click}
                              className=" py-1 rounded text-gray-500"
                            >
                              Schedule_BK
                            </button>
                            <hr className="border border-gray-500" />
                            <button
                              onClick={Schedule_RE_Click}
                              className=" py-1 rounded text-gray-500 "
                            >
                              Schedule_RE
                            </button>
                          </div>
                        </th>
                      )}
                      {columnVisibility.PI_Machine && (
                        <th className="py-1 w-10" rowSpan="2">
                          PI_Machine Lot_Time (min/Lot)
                        </th>
                      )}
                      {columnVisibility.PI_Person && (
                        <th className="py-1 w-10" rowSpan="2">
                          PI_Person Lot_time (min/Lot)
                        </th>
                      )}
                    </tr>
                    {columnVisibility.Quoteinfo && (
                      <tr className="text-black font-bold text-[11px]">
                        <th className="py-1 px-2 w-auto ">No.</th>
                        <th className="py-1 px-5 w-auto">M|P(m/p)</th>
                      </tr>
                    )}
                  </thead>

                  <tbody>
                    {rows.map((row, rowIndex) => {
                      const isActive = activeRowIndex === rowIndex;
                      const n = rowIndex + 1;

                      return (
                        <tr
                          key={rowIndex}
                          onFocusCapture={() => setActiveRowIndex(rowIndex)}
                          className={`border border-black text-[10px] ${
                            activeRowIndex === rowIndex ? "bg-gray-300" : ""
                          }`}
                        >
                          {/* Column for Row Number */}
                          {!columnVisibility.Quoteinfo && (
                            <td className="sticky left-0 z-10 py-1 px-2 border border-black text-center w-auto bg-gray-300">
                              {rowIndex + 1}
                            </td>
                          )}

                          {columnVisibility.Quoteinfo && (
                            <>
                              <td className="py-1 px-2 border border-black text-center w-auto bg-gray-300">
                                {rowIndex + 1}
                              </td>
                              <td className="py-1 px-2 border border-black text-center w-auto bg-gray-300">
                                <div className="text-center">{row.mp}</div>
                              </td>
                              <td className="py-1 px-2 border border-black text-center w-auto align-center bg-gray-300">
                                <div>
                                  <button
                                    disabled={!buttonState.Quoteinfo}
                                    onClick={() => quoteAllClick(n)}
                                    className={`bg-gray-100 py-1 px-2 rounded h-8 font-bold  
                                            ${
                                              buttonState.Quoteinfo
                                                ? "text-gray-900"
                                                : "text-gray-500"
                                            }`}
                                  >
                                    ➔
                                  </button>
                                </div>
                              </td>
                            </>
                          )}
                          <td className="py-1 px-2 border border-black text-center w-auto align-center">
                            <div className="text-center">
                              {row.plan_process}
                            </div>
                          </td>
                          <td className="py-1 px-2 border border-black text-center w-auto align-center">
                            <div className="text-center">{row.min}</div>
                          </td>
                          <td className="py-1 px-2 border border-black text-center w-auto align-center">
                            <div className="text-center">{row.mind}</div>
                          </td>
                          <td className="py-1 px-2 border border-black text-center w-auto align-center">
                            <div className="text-center">{row.time}</div>
                          </td>
                          <td className="py-1 px-2 border border-black text-center w-auto align-center">
                            <div className="text-center">{row.plan_date}</div>
                          </td>
                          <td className="py-1 px-2 border border-black text-center w-auto align-center">
                            <div className="text-center">
                              {row.instructions}
                            </div>
                          </td>
                          <td className="py-1 px-2 border border-black text-center w-auto align-center">
                            <div className="text-center">{row.result_date}</div>
                          </td>
                          <td className="py-1 px-2 border border-black text-center w-auto align-center">
                            <div className="text-center">
                              {row.resultmachine}
                            </div>
                          </td>
                          <td className="py-1 px-2 border border-black text-center w-auto align-center">
                            <div className="text-center">
                              {row.result_person}
                            </div>
                          </td>
                          <td className="py-1 px-2 border border-black text-center w-auto align-center">
                            <div className="text-center">{row.resultqty}</div>
                          </td>
                          {columnVisibility.ASP_Schedule && (
                            <td className="py-1 px-2 border border-black text-center w-auto align-center bg-gray-300">
                              <div className="text-center">{row.asp}</div>
                            </td>
                          )}
                          {columnVisibility.Schedule_BK_RE && (
                            <td className="py-1 px-2 border border-black text-center w-auto align-center bg-gray-300">
                              <div className="text-center">{row.re}</div>
                            </td>
                          )}
                          {columnVisibility.PI_Machine && (
                            <td className="py-1 px-2 border border-black text-center w-auto align-center bg-gray-300 ">
                              <div className="text-center">
                                {row.pi_machine}
                              </div>
                            </td>
                          )}
                          {columnVisibility.PI_Person && (
                            <td className="py-1 px-2 border border-black text-center w-auto align-center bg-gray-300">
                              <div className="text-center">{row.pi_person}</div>
                            </td>
                          )}
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
        <div className="flex flex-col lg:flex-row justify-between items-center mt-3 bg-white h-20 sm:h-16 md:h-20 scale-100 sm:scale-100 md:scale-100 transition-transform">
          <div className="p-3 sm:p-2 md:p-1 w-auto">
            <div className="grid sm:grid-cols-2 md:grid-cols-3 gap-4 sm:gap-3 md:gap-2">
              <div className="grid grid-cols-4 gap-2 sm:gap-1 mt-1">
                <button
                  id="F1"
                  disabled={!buttonState.F1}
                  className="h-11 w-auto text-[10px] bg-blue-500 p-1 rounded-lg hover:bg-blue-700 font-medium text-white text-center disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500 
             sm:h-11 sm:w-auto md:h-12 md:w-14 lg:h-14 lg:w-14 xl:h-16 xl:w-[90px] xl:text-[13px]"
                  onClick={() => {
                    const orderNo = SearchorderNoRef.current?.value; // ดึงค่าจาก input
                    if (orderNo) {
                      window.open(
                        `/plan-info-copy?orderNo=${encodeURIComponent(
                          orderNo
                        )}`,
                        "_blank",
                        "width=800,height=600"
                      );
                    }
                  }}
                >
                  Plan Copy <br /> 引用 (F1)
                </button>

                <button
                  id="F2"
                  disabled={!buttonState.F2}
                  onClick={handleF2Click}
                  className="h-11 w-auto text-[10px] bg-blue-500 p-1 rounded-lg hover:bg-blue-700 font-medium text-white text-center disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500 
             sm:h-11 sm:w-auto md:h-12 md:w-14 lg:h-14 lg:w-14 xl:h-16 xl:w-[90px] xl:text-[13px]"
                >
                  Edit <br /> 編集 (F2)
                </button>
                <button
                  id="F3"
                  disabled={!buttonState.F3}
                  onClick={handleF3Click}
                  className="h-11 w-auto text-[10px] bg-blue-500 p-1 rounded-lg hover:bg-blue-700 font-medium text-white text-center disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500 
             sm:h-11 sm:w-auto md:h-12 md:w-14 lg:h-14 lg:w-14 xl:h-16 xl:w-[90px] xl:text-[13px]"
                >
                  New Add <br /> 追加 (F3)
                </button>

                <button
                  id="F4"
                  disabled={!buttonState.F4}
                  onClick={handleF4Click}
                  className="h-11 w-auto text-[10px] bg-blue-500 p-1 rounded-lg hover:bg-blue-700 font-medium text-white text-center disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500 
             sm:h-11 sm:w-auto md:h-12 md:w-14 lg:h-14 lg:w-14 xl:h-16 xl:w-[90px] xl:text-[13px]"
                >
                  Sub-Con <br /> 手配 (F4)
                </button>
              </div>
              <div className="grid grid-cols-4 gap-2 mt-1">
                <button
                  id="F5"
                  disabled={!buttonState.F5}
                  className="h-11 w-auto text-[10px] bg-blue-500 p-1 rounded-lg hover:bg-blue-700 font-medium text-white text-center disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500 
             sm:h-11 sm:w-auto md:h-12 md:w-14 lg:h-14 lg:w-14 xl:h-16 xl:w-[90px] xl:text-[13px]"
                >
                  Plan <br /> 計画 (F5)
                </button>
                <button
                  id="F6"
                  disabled={!buttonState.F6}
                  onClick={handleF6Click}
                  className="h-11 w-auto text-[10px] bg-blue-500 p-1 rounded-lg hover:bg-blue-700 font-medium text-white text-center disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500 
             sm:h-11 sm:w-auto md:h-12 md:w-14 lg:h-14 lg:w-14 xl:h-16 xl:w-[90px] xl:text-[13px]"
                >
                  P Sheet All <br /> 全頁 (F6)
                </button>
                <button
                  id="F7"
                  disabled={!buttonState.F7}
                  onClick={handleF7Click}
                  className="h-11 w-auto text-[10px] bg-blue-500 p-1 rounded-lg hover:bg-blue-700 font-medium text-white text-center disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500 
             sm:h-11 sm:w-auto md:h-12 md:w-14 lg:h-14 lg:w-14 xl:h-16 xl:w-[90px] xl:text-[13px]"
                >
                  P Sheet 1P <br /> 1頁 (F7)
                </button>
                <button
                  id="F8"
                  disabled={!buttonState.F8}
                  onClick={handleF8Click}
                  className="h-11 w-auto text-[10px] bg-blue-500 p-1 rounded-lg hover:bg-blue-700 font-medium text-white text-center disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500 
             sm:h-11 sm:w-auto md:h-12 md:w-14 lg:h-14 lg:w-14 xl:h-16 xl:w-[90px] xl:text-[13px]"
                >
                  Next Parts <br /> 別部 (F8)
                </button>
              </div>
              <div className="grid grid-cols-4 gap-2 mt-1">
                <button
                  id="F9"
                  disabled={!buttonState.F9}
                  onClick={handleF9Click}
                  className="h-11 w-auto text-[10px] bg-blue-500 p-1 rounded-lg hover:bg-blue-700 font-medium text-white text-center disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500 
             sm:h-11 sm:w-auto md:h-12 md:w-14 lg:h-14 lg:w-14 xl:h-16 xl:w-[90px] xl:text-[13px]"
                >
                  Save <br /> 登録 (F9)
                </button>
                <button
                  id="F10"
                  disabled={!buttonState.F10}
                  onClick={handleF10Click}
                  className="h-11 w-auto text-[10px] bg-blue-500 p-1 rounded-lg hover:bg-blue-700 font-medium text-white text-center disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500 
             sm:h-11 sm:w-auto md:h-12 md:w-14 lg:h-14 lg:w-14 xl:h-16 xl:w-[90px] xl:text-[13px]"
                >
                  Delete <br /> 削除 (F10)
                </button>
                <button
                  id="F11"
                  disabled={!buttonState.F11}
                  onClick={handleF11Click}
                  className="h-11 w-auto text-[10px] bg-blue-500 p-1 rounded-lg hover:bg-blue-700 font-medium text-white text-center disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500 
             sm:h-11 sm:w-auto md:h-12 md:w-14 lg:h-14 lg:w-14 xl:h-16 xl:w-[90px] xl:text-[13px]"
                >
                  Next Input <br /> 次へ (F11)
                </button>
                <button
                  id="F12"
                  disabled={buttonState.F12}
                  onClick={handleF12Click}
                  className="h-11 w-auto text-[10px] bg-blue-500 p-1 rounded-lg hover:bg-blue-700 font-medium text-white text-center disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500 
             sm:h-11 sm:w-auto md:h-12 md:w-14 lg:h-14 lg:w-14 xl:h-16 xl:w-[90px] xl:text-[13px]"
                >
                  Exit <br /> 終了 (F12)
                </button>
              </div>
            </div>
          </div>
          <div className="max-w-[200px] ml-auto py-0.5">
            <div className="flex flex-wrap gap-0.5 items-end">
              {/* MaxEnd_No */}
              <div className="flex items-center w-full gap-0.5">
                <label
                  htmlFor="max-end-no"
                  className="text-[10px] text-right pr-0.5"
                >
                  MaxEnd_No
                </label>
                <input
                  disabled
                  id="Max_No"
                  value={planData?.Max_No || 0}
                  onChange={handlePlanInputChange}
                  type="text"
                  className="border border-gray-500 rounded p-0.5 text-[10px] w-8"
                />
                <input
                  disabled
                  id="End_No"
                  value={planData?.End_No || 0}
                  onChange={handlePlanInputChange}
                  type="text"
                  className="border border-gray-500 rounded p-0.5 text-[10px] w-8"
                />
                <label
                  htmlFor="total"
                  className="text-[10px] text-right pr-0.5"
                >
                  Total
                </label>
                <input
                  disabled
                  id="Total_P_Time"
                  value={planData?.Total_P_Time || 0}
                  onChange={handlePlanInputChange}
                  type="text"
                  className="border border-gray-500 rounded p-0.5 text-[10px] w-12"
                />
              </div>

              {/* Now_No */}
              <div className="flex items-center w-full pl-2">
                <label htmlFor="now-no" className="text-[10px] text-right pr-2">
                  Now_No
                </label>
                <input
                  disabled
                  id="Now_No"
                  value={planData?.Now_No || 0}
                  onChange={handlePlanInputChange}
                  type="text"
                  className="border border-gray-500 rounded p-0.5 text-[10px] w-10"
                />
                <label
                  htmlFor="re-total"
                  className="text-[10px] text-right pr-2 pl-3"
                >
                  Re_Total
                </label>
                <input
                  disabled
                  id="Re_Total_P_Time"
                  value={planData?.Re_Total_P_Time || 0}
                  onChange={handlePlanInputChange}
                  type="text"
                  className="border border-gray-500 rounded p-0.5 text-[10px] w-12"
                />
              </div>

              {/* Re_Pr_Qty */}
              <div className="flex items-center w-full">
                <label
                  htmlFor="re-pr-qty"
                  className="text-[10px] text-right pr-0.5 pl-2"
                >
                  Re_Pr_Qty
                </label>
                <input
                  disabled
                  id="Re_Pr_Qty"
                  value={planData?.Re_Pr_Qty || 0}
                  onChange={handlePlanInputChange}
                  type="text"
                  className="border border-gray-500 rounded p-0.5 text-[10px] w-10"
                />
                <label
                  htmlFor="re-total-n"
                  className="text-[10px] text-right pr-0.5 pl-2"
                >
                  Re_Total_N
                </label>
                <input
                  disabled
                  id="Re_Total_N_Time"
                  value={planData?.Re_Total_N_Time || 0}
                  onChange={handlePlanInputChange}
                  type="text"
                  className="border border-gray-500 rounded p-0.5 text-[10px] w-12"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
